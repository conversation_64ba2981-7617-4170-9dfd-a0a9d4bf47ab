package com.yuanshuo.platform.order.server;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.platform.order.api.enums.EnumFulfillmentStatus;
import com.yuanshuo.platform.order.api.enums.EnumOrderPaymentStatus;
import com.yuanshuo.platform.order.api.enums.EnumOrderRefundStatus;
import com.yuanshuo.platform.order.api.enums.EnumTradeOrderStatus;
import com.yuanshuo.platform.order.api.model.dto.AuditTradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.OrderAddressDTO;
import com.yuanshuo.platform.order.api.model.dto.TradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.TradeOrderItemDTO;
import com.yuanshuo.platform.order.api.model.dto.query.*;
import com.yuanshuo.platform.order.api.model.vo.*;
import com.yuanshuo.platform.order.common.BusinessException;
import com.yuanshuo.platform.order.entity.DataSnapshotEntity;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.flow.context.PayPaidContext;
import com.yuanshuo.platform.order.flow.context.order.CreateOrderContext;
import com.yuanshuo.platform.order.mapper.*;
import com.yuanshuo.platform.order.mapper.po.*;
import com.yuanshuo.platform.order.mapper.query.TradeOrderLogQuery;
import com.yuanshuo.platform.order.mapper.query.TradeOrderQuery;
import com.yuanshuo.platform.order.util.DeepCopyUtil;
import com.yuanshuo.platform.order.util.LockExecutorUtil;
import com.yuanshuo.platform.order.util.PageUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TradeOrderService {
    @Autowired
    TradeOrderMapper tradeOrderMapper;
    @Autowired
    OrderAddressMapper orderAddressMapper;
    @Autowired
    OrderItemMapper orderItemMapper;
    @Autowired
    OrderExtendsMapper orderExtendsMapper;
    @Autowired
    OrderItemExtendsMapper orderItemExtendsMapper;
    @Autowired
    OrderOperationLogMapper orderOperationLogMapper;
    @Autowired
    OrderStatusHistoryMapper orderStatusHistoryMapper;
    @Autowired
    OrderRefundMapper orderRefundMapper;
    
    @Autowired
    OrderPriceChangeService orderPriceChangeService;
    
    @Autowired
    OrderRefundService orderRefundService;

    @Resource
    private LockExecutorUtil lockExecutorUtil;


    //创建订单
    @Transactional(rollbackFor = Exception.class)
    public TradeOrderVO createOrder(CreateOrderContext createOrderContext){
        TradeOrderDTO tradeOrderDTO = createOrderContext.getTradeOrderDTO();

        OrderAddressDTO orderAddressDTO = tradeOrderDTO.getOrderAddress();

        TradeOrder tradeOrder = new TradeOrder();
        Long userId = tradeOrderDTO.getUserId();
        tradeOrder.setUserId(userId);

        //订单号可以通过编排传入进来
        String tradeOrderNo = createOrderContext.getGenerateOrderNo().get();
        String orderItemTradeOrderNo = tradeOrderNo;
        tradeOrder.setTradeOrderNo(tradeOrderNo);

        BeanUtil.copyProperties(orderAddressDTO,tradeOrder);
        BeanUtil.copyProperties(tradeOrderDTO,tradeOrder);

//        tradeOrder.setStatus(EnumTradeOrderStatus.PENDING_PAYMENT.getCode());
        /**
         * status:
         * initialize(初始化)
         * 其他状态均为空
         */
        tradeOrder.setStatus(EnumTradeOrderStatus.INITIALIZE.getCode());
        tradeOrder.setReserveTime(tradeOrderDTO.getReserveTime());
        //插入地址表
        OrderAddress orderAddress = new OrderAddress();
        BeanUtil.copyProperties(orderAddressDTO,orderAddress);
        orderAddress.setUserId(userId);
        orderAddress.setTradeOrderNo(tradeOrderNo);
        orderAddress.setCreateUser(String.valueOf(tradeOrderDTO.getOperatorUserId()));
        orderAddressMapper.insertSelective(orderAddress);
        tradeOrder.setOrderAddressId(orderAddress.getId());
        tradeOrder.setActualOrderType(tradeOrderDTO.getActualOrderType());
        tradeOrder.setPayChannel(tradeOrderDTO.getPayChannel());
        tradeOrder.setTotalPayable(tradeOrderDTO.getTotalAmount()- tradeOrderDTO.getTotalDiscount());
        /**
         * 订单详情在app下单的情况下 如果无子订单 则按照原来的逻辑
         * 如果有子订单，则将 费用详情绑定在第一个子订单上
         */
        // 插入订单详情表
        List<TradeOrderItemDTO> tradeOrderItems = tradeOrderDTO.getTradeOrderItems();
        List<OrderItem> orderItemList = new ArrayList<>();
        List<OrderItemExtends> orderItemExtendsList = new ArrayList<>();
        int totalAmount = 0;
        for (TradeOrderItemDTO TradeOrderItemDTO : tradeOrderItems) {
            OrderItem orderItem = new OrderItem();
            BeanUtil.copyProperties(TradeOrderItemDTO,orderItem);
            orderItem.setOrderItemNo(createOrderContext.getGenerateItemOrderNo().get());
            Integer price = orderItem.getPrice();
            Integer quantity = orderItem.getQuantity();
            if (price != null && quantity != null) {
                int subtotal = orderItem.getPrice() * orderItem.getQuantity();
                orderItem.setSubtotal(subtotal);
                totalAmount = totalAmount + subtotal;
            }
            orderItem.setCreateUser(String.valueOf(tradeOrderDTO.getOperatorUserId()));

            orderItemList.add(orderItem);

            //插入订单详情扩展表
            OrderItemExtends orderItemExtends = new OrderItemExtends();
            orderItemExtends.setOrderItemNo(orderItem.getOrderItemNo());
            orderItemExtends.setCreateUser(String.valueOf(userId));
            orderItemExtends.setExtData(TradeOrderItemDTO.getItemExtData());

            orderItemExtendsList.add(orderItemExtends);

        }


        List<TradeOrder> tradeOrderList = new ArrayList<>();
        //如果有子订单
        List<TradeOrderDTO> childOrders = tradeOrderDTO.getChildOrders();
        if(CollUtil.isNotEmpty(childOrders)){
            int count = 0;
            for (TradeOrderDTO childOrderDTO : childOrders) {

                TradeOrder childTradeOrder = new TradeOrder();

                OrderAddressDTO childOrderAddressDTO = childOrderDTO.getOrderAddress();
                childTradeOrder.setUserId(userId);

                childTradeOrder.setParentNo(tradeOrderNo);
                //订单号可以通过编排传入进来
                String childTradeOrderNo = createOrderContext.getChildGenerateOrderNo().get();
                childTradeOrder.setTradeOrderNo(childTradeOrderNo);

                BeanUtil.copyProperties(childOrderAddressDTO,childTradeOrder);
                BeanUtil.copyProperties(childOrderDTO,childTradeOrder);


                childTradeOrder.setStatus(EnumTradeOrderStatus.INITIALIZE.getCode());
                childTradeOrder.setReserveTime(tradeOrderDTO.getReserveTime());
                childTradeOrder.setParentType(false);
                //插入地址表
                OrderAddress childOrderAddress = new OrderAddress();
                BeanUtil.copyProperties(childOrderAddressDTO,childOrderAddress);
                childOrderAddress.setUserId(userId);
                childOrderAddress.setTradeOrderNo(childTradeOrderNo);
                childOrderAddress.setCreateUser(String.valueOf(tradeOrderDTO.getOperatorUserId()));

                childTradeOrder.setOrderAddressId(childOrderAddress.getId());


                if(count == 0){
                    //todo 第一版本 先将费用详情绑定在第一个子订单上
                    orderItemTradeOrderNo = childTradeOrderNo;
                    childTradeOrder.setTotalAmount(totalAmount);
                    childTradeOrder.setTotalDiscount(0);
                }
                orderAddressMapper.insertSelective(childOrderAddress);
                childTradeOrder.setOrderAddressId(childOrderAddress.getId());
                tradeOrderMapper.insertSelective(childTradeOrder);

                //插入订单扩展表
                OrderExtends orderExtends = new OrderExtends();
                orderExtends.setOrderNo(childTradeOrderNo);
                orderExtends.setCreateUser(String.valueOf(userId));
                orderExtends.setExtData(childOrderDTO.getOrderExtData());
                orderExtendsMapper.insertSelective(orderExtends);

                tradeOrderList.add(childTradeOrder);

                count++;

                tradeOrder.setParentType(true);
            }

            tradeOrder.setTotalAmount(totalAmount);
        }

        //暂时无优惠 todo
        tradeOrder.setTotalDiscount(0);
        tradeOrderMapper.insertSelective(tradeOrder);
        //插入订单扩展表
        OrderExtends orderExtends = new OrderExtends();
        orderExtends.setOrderNo(tradeOrderNo);
        orderExtends.setCreateUser(String.valueOf(userId));
        orderExtends.setExtData(tradeOrderDTO.getOrderExtData());
        orderExtendsMapper.insertSelective(orderExtends);

        //插入订单明细表
        for (OrderItem orderItem : orderItemList) {
            orderItem.setTradeOrderNo(orderItemTradeOrderNo);
            orderItemMapper.insertSelective(orderItem);
        }
        //插入订单明细扩展表
        for (OrderItemExtends orderItemExtends : orderItemExtendsList) {
            orderItemExtends.setTradeOrderNo(orderItemTradeOrderNo);
            orderItemExtendsMapper.insertSelective(orderItemExtends);
        }

        DataSnapshotEntity afterDataSnapshotEntity = new DataSnapshotEntity();
        //订单基础数据快照
        afterDataSnapshotEntity.setTradeOrder(tradeOrder);
        //订单地址数据快照
        afterDataSnapshotEntity.setOrderAddress(orderAddress);
        //订单扩展数据快照
        afterDataSnapshotEntity.setOrderExtends(orderExtends);
        //订单明细数据快照
        afterDataSnapshotEntity.setOrderItemList(orderItemList);
        //订单明细扩展数据快照
        afterDataSnapshotEntity.setOrderItemExtendsList(orderItemExtendsList);


        insertOrderStatusHistoryAndLog(new DataSnapshotEntity(),afterDataSnapshotEntity, createOrderContext);
        TradeOrderVO tradeOrderVO = buildTradeOrderVO(tradeOrder);
        if(CollUtil.isNotEmpty(tradeOrderList)){
            List<TradeOrderVO> childTradeOrderVOS = new ArrayList<>();
            for (TradeOrder childTradeOrder : tradeOrderList) {
                childTradeOrderVOS.add(buildTradeOrderVO(childTradeOrder));
            }
            tradeOrderVO.setChildOrders(childTradeOrderVOS);
        }
        return tradeOrderVO;
    }

    //审核订单
    @Transactional(rollbackFor = Exception.class)
    public void auditOrder(AuditTradeOrderDTO auditTradeOrderDTO) {
        String tradeOrderNo = auditTradeOrderDTO.getTradeOrderNo();
        TradeOrder dataBaseTradeOrder = queryByOrderNoMust(tradeOrderNo);
        updateOrderByTradeOrder(dataBaseTradeOrder,new BaseContext());
    }


    /**
     * 支付成功回调
     * @param payPaidContext
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderToPaid(PayPaidContext payPaidContext) {
        String tradeOrderNo = payPaidContext.getTradeOrderNo();
        TradeOrder tradeOrder = new TradeOrder();
        tradeOrder.setTradeOrderNo(tradeOrderNo);
        //支付成功
        tradeOrder.setPaymentStatus(EnumOrderPaymentStatus.PAID.getCode());
        tradeOrder.setTotalPaid(payPaidContext.getAmount());
        tradeOrder.setPayChannel(payPaidContext.getPayChannel());
        updateOrderByTradeOrder(tradeOrder, payPaidContext);
    }

    //查询订单
    public TableDataInfo<TradeOrderVO> queryPageOrder(TradeOrderQueryPageDTO tradeOrderQueryPageDTO) {
        PageUtil.startPage(tradeOrderQueryPageDTO);
        TradeOrderQuery tradeOrderQuery = new TradeOrderQuery();
        BeanUtil.copyProperties(tradeOrderQueryPageDTO, tradeOrderQuery);
        List<TradeOrder> tradeOrders = tradeOrderMapper.selectByQuery(tradeOrderQuery);
        List<TradeOrderVO> TradeOrderVOS = buildTradeOrderVOS(tradeOrders);
        //构建tabledata
        PageInfo page = new PageInfo(tradeOrders);
        TableDataInfo<TradeOrderVO> tableData = new TableDataInfo<>();
        tableData.setTotal(page.getTotal());
        tableData.setRows(TradeOrderVOS);
        return tableData;
    }



    private List<TradeOrderVO> buildTradeOrderVOS(List<TradeOrder> tradeOrders) {
        List<TradeOrderVO> TradeOrderVOS = new ArrayList<>();
        //遍历为每个订单填充item
        for (TradeOrder tradeOrder : tradeOrders) {
            TradeOrderVOS.add(buildTradeOrderVO(tradeOrder));
        }
        return TradeOrderVOS;
    }

    private TradeOrderVO buildTradeOrderVO(TradeOrder tradeOrder) {
        TradeOrderVO tradeOrderVO = new TradeOrderVO();
        BeanUtil.copyProperties(tradeOrder,tradeOrderVO);
        OrderItem itemQuery = new OrderItem();
        itemQuery.setTradeOrderNo(tradeOrder.getTradeOrderNo());
        List<OrderItem> orderItems = orderItemMapper.select(itemQuery);
        List<OrderItemVO> orderItemVOS = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            OrderItemVO orderItemVO = new OrderItemVO();
            //填充扩展信息
            OrderItemExtends query = new OrderItemExtends();
            query.setOrderItemNo(orderItem.getOrderItemNo());
            OrderItemExtends orderItemExtends = orderItemExtendsMapper.selectOne(query);
            if (orderItemExtends != null) {
                orderItemVO.setItemExtData(orderItemExtends.getExtData());
            }
            BeanUtil.copyProperties(orderItem,orderItemVO);
            orderItemVOS.add(orderItemVO);
        }
        tradeOrderVO.setOrderItems(orderItemVOS);
        //填充订单扩展信息
        OrderExtends query = new OrderExtends();
        query.setOrderNo(tradeOrder.getTradeOrderNo());
        OrderExtends orderExtends = orderExtendsMapper.selectOne(query);
        if (orderExtends != null) {
            tradeOrderVO.setOrderExtData(orderExtends.getExtData());
        }
        //填充地址
        OrderAddress orderAddress = orderAddressMapper.selectByPrimaryKey(tradeOrder.getOrderAddressId());
        if(orderAddress != null){
            OrderAddressVO orderAddressVO = new OrderAddressVO();
            BeanUtil.copyProperties(orderAddress,orderAddressVO);
            tradeOrderVO.setOrderAddress(orderAddressVO);
        }
        List<OrderRefundVO> orderRefundVOS = orderRefundService.queryOrderRefund(tradeOrder.getTradeOrderNo());
        if(CollUtil.isNotEmpty(orderRefundVOS)){
            tradeOrderVO.setOrderRefundVOList(orderRefundVOS);
        }
        //填充orderPriceChangeVOS
        List<OrderPriceChangeVO> orderPriceChangeVOS = orderPriceChangeService.getPriceChangeHistory(tradeOrder.getTradeOrderNo());
        if(CollUtil.isNotEmpty(orderPriceChangeVOS)){
            tradeOrderVO.setOrderPriceChangeVOS(orderPriceChangeVOS);
        }
        
        return tradeOrderVO;
    }

    public void updateOrder(TradeOrder tradeOrder) {
        if (tradeOrder.getId() == null && tradeOrder.getTradeOrderNo() == null) {
            throw new BusinessException("参数异常");
        }
        if (tradeOrder.getId() == null) {
            TradeOrder databaseTradeOrder = queryByOrderNoMust(tradeOrder.getTradeOrderNo());
            tradeOrder.setId(databaseTradeOrder.getId());
        }
        tradeOrderMapper.updateByPrimaryKeySelective(tradeOrder);
    }


    /**
     * 后续更新 不管 是订单 还是订单地址 还是扩展数据 还是订单明细以及订单明细扩展 都需要先查询出快照数据
     * @param tradeOrderNo
     * @return
     */
    public DataSnapshotEntity buildDataSnapshotEntityBeforeUpdate(String tradeOrderNo) {

        //快照数据前查询
        //订单主表
        TradeOrder tradeOrder = queryByOrderNoMust(tradeOrderNo);
        //地址表
        OrderAddress orderAddress = queryOrderAddressByOrderNo(tradeOrderNo);
        //订单扩展表
        OrderExtends orderExtends = queryOrderExtendsByOrderNo(tradeOrderNo);
        //订单明细表
        List<OrderItem> orderItemList = queryOrderItemByOrderNo(tradeOrderNo);
        //订单明细扩充表与 订单明细表一一对应
        List<OrderItemExtends> orderItemExtendsList =queryOrderItemExtendsByOrderNo(tradeOrderNo);

        //查询退款订单表
        List<OrderRefund> orderRefundList =queryOrderRefundByOrderNo(tradeOrderNo);

        DataSnapshotEntity dataSnapshotEntity = new DataSnapshotEntity();


        //订单基础数据快照
        dataSnapshotEntity.setTradeOrder(tradeOrder);

        //订单地址数据快照
        dataSnapshotEntity.setOrderAddress(orderAddress);

        //订单扩展数据快照
        dataSnapshotEntity.setOrderExtends(orderExtends);

        //订单明细数据快照
        dataSnapshotEntity.setOrderItemList(orderItemList);

        //订单明细扩展数据快照
        dataSnapshotEntity.setOrderItemExtendsList(orderItemExtendsList);

        //退款订单数据快照
        dataSnapshotEntity.setOrderRefundList(orderRefundList);

        return dataSnapshotEntity;
    }



    /**
     * 更新主表 trade_order
     *
     * @param tradeOrder
     * @param baseContext
     */

    public void updateOrderByTradeOrder(TradeOrder tradeOrder, BaseContext baseContext) {

        if (Objects.isNull(tradeOrder) || tradeOrder.getTradeOrderNo() == null) {
            throw new BusinessException("参数异常");
        }
        DataSnapshotEntity beforeDataSnapshotEntity = buildDataSnapshotEntityBeforeUpdate(tradeOrder.getTradeOrderNo());

        tradeOrder.setId(beforeDataSnapshotEntity.getTradeOrder().getId());
        tradeOrderMapper.updateByPrimaryKeySelective(tradeOrder);

        DataSnapshotEntity afterDataSnapshotEntity = buildAfterDataSnapshotEntityWithUpdateDate(tradeOrder, beforeDataSnapshotEntity);

        insertOrderStatusHistoryAndLog(beforeDataSnapshotEntity,afterDataSnapshotEntity,baseContext);
    }

    /**
     * 更新订单地址表 order_address
     * @param orderAddress
     */
    public void updateOrderByOrderAddress(OrderAddress orderAddress ,BaseContext baseContext) {
        if (Objects.isNull(orderAddress) || orderAddress.getTradeOrderNo() == null) {
            throw new BusinessException("参数异常");
        }
        DataSnapshotEntity beforeDataSnapshotEntity = buildDataSnapshotEntityBeforeUpdate(orderAddress.getTradeOrderNo());
        if(Objects.isNull(beforeDataSnapshotEntity.getOrderAddress()) || beforeDataSnapshotEntity.getOrderAddress().getId() == null){
            throw new BusinessException("参数异常");
        }

        orderAddress.setId(beforeDataSnapshotEntity.getOrderAddress().getId());
        orderAddressMapper.updateByPrimaryKeySelective(orderAddress);
        DataSnapshotEntity afterDataSnapshotEntity = buildAfterDataSnapshotEntityWithUpdateDate(orderAddress, beforeDataSnapshotEntity);

        insertOrderStatusHistoryAndLog(beforeDataSnapshotEntity,afterDataSnapshotEntity,baseContext);
    }
    /**
     * 更新订单扩展表 order_extends
     */
    public void updateOrderByOrderExtends(OrderExtends orderExtends, BaseContext baseContext) {
        if (Objects.isNull(orderExtends) || orderExtends.getOrderNo() == null) {
            throw new BusinessException("参数异常");
        }
        DataSnapshotEntity beforeDataSnapshotEntity = buildDataSnapshotEntityBeforeUpdate(orderExtends.getOrderNo());
        if(Objects.isNull(beforeDataSnapshotEntity.getOrderExtends()) || beforeDataSnapshotEntity.getOrderExtends().getId() == null){
            throw new BusinessException("参数异常");
        }
        orderExtends.setId(beforeDataSnapshotEntity.getOrderExtends().getId());
        orderExtendsMapper.updateByPrimaryKeySelective(orderExtends);
        DataSnapshotEntity afterDataSnapshotEntity = buildAfterDataSnapshotEntityWithUpdateDate(orderExtends, beforeDataSnapshotEntity);

        insertOrderStatusHistoryAndLog(beforeDataSnapshotEntity,afterDataSnapshotEntity,baseContext);
    }

    /**
     * 更新trade_order 表数据时 启用此方法 获取更新后的快照
     * @param
     * @param beforeDataSnapshotEntity
     * @return
     */
    public DataSnapshotEntity buildAfterDataSnapshotEntityWithUpdateDate(TradeOrder tradeOrder, DataSnapshotEntity beforeDataSnapshotEntity) {

        DataSnapshotEntity dataSnapshotEntity = DeepCopyUtil.deepCopyByJson(beforeDataSnapshotEntity, DataSnapshotEntity.class);
        //修改的字段
        TradeOrder tradeOrderAfter = dataSnapshotEntity.getTradeOrder();
        CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
        BeanUtil.copyProperties(tradeOrder,tradeOrderAfter,copyOptions);
        dataSnapshotEntity.setTradeOrder(tradeOrderAfter);
        return dataSnapshotEntity;
    }

    /**
     * 同时更新trade_order 与order_refund 表数据时 启用此方法 获取更新后的快照
     * @param orderRefund
     * @param beforeDataSnapshotEntity
     * @return
     */
    public DataSnapshotEntity buildAfterDataSnapshotEntityWithUpdateDate( OrderRefund orderRefund, DataSnapshotEntity beforeDataSnapshotEntity) {
        DataSnapshotEntity dataSnapshotEntity = DeepCopyUtil.deepCopyByJson(beforeDataSnapshotEntity, DataSnapshotEntity.class);
        //修改的字段
        TradeOrder tradeOrderAfter = dataSnapshotEntity.getTradeOrder();
        dataSnapshotEntity.setTradeOrder(tradeOrderAfter);

        List<OrderRefund> orderRefundList = new ArrayList<>();
        if(CollUtil.isNotEmpty(dataSnapshotEntity.getOrderRefundList())){
            for (OrderRefund refund : dataSnapshotEntity.getOrderRefundList()) {
                if(refund.getId().equals(orderRefund.getId())){
                    orderRefundList.add(orderRefund);
                }else{
                    orderRefundList.add(refund);
                }
            }
        }else{
            orderRefundList.add(orderRefund);
        }
        dataSnapshotEntity.setOrderRefundList(orderRefundList);
        return dataSnapshotEntity;
    }
    /**
     * 更新order_address 表数据时 启用此方法 获取更新后的快照
     * @param orderAddress
     * @param beforeDataSnapshotEntity
     * @return
     */
    public DataSnapshotEntity buildAfterDataSnapshotEntityWithUpdateDate(OrderAddress orderAddress, DataSnapshotEntity beforeDataSnapshotEntity) {
        //未修改的字段
        DataSnapshotEntity dataSnapshotEntity = DeepCopyUtil.deepCopyByJson(beforeDataSnapshotEntity,DataSnapshotEntity.class);
        //修改的字段
        OrderAddress orderAddressAfter = dataSnapshotEntity.getOrderAddress();
        CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
        BeanUtil.copyProperties(orderAddress,orderAddressAfter,copyOptions);
        dataSnapshotEntity.setOrderAddress(orderAddressAfter);
        return dataSnapshotEntity;
    }

    /**
     * 更新order_address 表数据时 启用此方法 获取更新后的快照
     * @param orderExtends
     * @param beforeDataSnapshotEntity
     * @return
     */
    public DataSnapshotEntity buildAfterDataSnapshotEntityWithUpdateDate(OrderExtends orderExtends, DataSnapshotEntity beforeDataSnapshotEntity) {
        //未修改的字段
        DataSnapshotEntity dataSnapshotEntity = DeepCopyUtil.deepCopyByJson(beforeDataSnapshotEntity,DataSnapshotEntity.class);
        //修改的字段
        OrderExtends orderExtendsAfter= dataSnapshotEntity.getOrderExtends();
        CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
        BeanUtil.copyProperties(orderExtends, orderExtendsAfter, copyOptions);

        dataSnapshotEntity.setOrderExtends(orderExtendsAfter);
        return dataSnapshotEntity;
    }
    /**
     * 订单状态变更记录以及快照记录
     *
     * @param beforeDataSnapshotEntity
     * @param afterDataSnapshotEntity
     * @param baseContext
     */
    public void insertOrderStatusHistoryAndLog(DataSnapshotEntity beforeDataSnapshotEntity, DataSnapshotEntity afterDataSnapshotEntity, BaseContext baseContext) {
        //order_status_history 记录订单总状态
        OrderStatusHistory orderStatusHistory = new OrderStatusHistory();
        orderStatusHistory.setTradeOrderNo(afterDataSnapshotEntity.getTradeOrder().getTradeOrderNo());
        TradeOrder tradeOrder = beforeDataSnapshotEntity.getTradeOrder();
        String beforeStatus = "";

        Long operatorUserId = baseContext.getOperatorUserId();
        String operatorUserName = baseContext.getOperatorUserName();
        if(!Objects.isNull(tradeOrder)){
            beforeStatus = beforeDataSnapshotEntity.getTradeOrder().getStatus();
        }
        orderStatusHistory.setBeforeStatus(beforeStatus);
        String afterStatus = afterDataSnapshotEntity.getTradeOrder().getStatus();
        orderStatusHistory.setAfterStatus(afterStatus);
        orderStatusHistory.setUserId(afterDataSnapshotEntity.getTradeOrder().getUserId());
        orderStatusHistory.setUserName(afterDataSnapshotEntity.getTradeOrder().getNickName());
        orderStatusHistory.setCreateUser(String.valueOf(operatorUserId));
        orderStatusHistory.setOperatorUserId(operatorUserId);
        orderStatusHistory.setOperatorUserName(operatorUserName);
        orderStatusHistory.setOperatorTime(new Date());

        orderStatusHistory.setNodeId(baseContext.getNodeId());
        orderStatusHistory.setNodeName(baseContext.getNodeName());
        orderStatusHistory.setChainId(baseContext.getChainId());
        orderStatusHistory.setChainName(baseContext.getChainName());
        String traceId = TraceContext.traceId();
        orderStatusHistory.setTraceId(traceId);
        if(Objects.isNull(tradeOrder) || !Objects.equals(beforeStatus,afterStatus)){
            orderStatusHistoryMapper.insertSelective(orderStatusHistory);
        }

        //test
        //order_operation_log更新
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setTradeOrderNo(afterDataSnapshotEntity.getTradeOrder().getTradeOrderNo());
        orderOperationLog.setBeforeData(JSONUtil.toJsonStr(beforeDataSnapshotEntity));
        orderOperationLog.setAfterData(JSONUtil.toJsonStr(afterDataSnapshotEntity));
        orderOperationLog.setCreateUser(String.valueOf(operatorUserId));
        orderOperationLog.setUserId(afterDataSnapshotEntity.getTradeOrder().getUserId());
        orderOperationLog.setUserName(afterDataSnapshotEntity.getTradeOrder().getNickName());
        orderOperationLog.setOperatorUserId(operatorUserId);
        orderOperationLog.setOperatorUserName(operatorUserName);
        orderOperationLog.setOperatorTime(new Date());

        orderOperationLog.setNodeId(baseContext.getNodeId());
        orderOperationLog.setNodeName(baseContext.getNodeName());
        orderOperationLog.setChainId(baseContext.getChainId());
        orderOperationLog.setChainName(baseContext.getChainName());

        orderOperationLog.setTraceId(traceId);
        orderOperationLogMapper.insertSelective(orderOperationLog);
    }


    public TradeOrder queryByOrderNoMust(String tradeOrderNo) {
        TradeOrder query = new TradeOrder();
        query.setTradeOrderNo(tradeOrderNo);
        TradeOrder tradeOrder = tradeOrderMapper.selectOne(query);
        if(Objects.isNull(tradeOrder)){
            throw new BusinessException("订单不存在");
        }
        return tradeOrder;
    }
    public OrderAddress queryOrderAddressByOrderNo(String tradeOrderNo) {
        OrderAddress query = new OrderAddress();
        query.setTradeOrderNo(tradeOrderNo);
        return orderAddressMapper.selectOne(query);
    }
    public OrderExtends queryOrderExtendsByOrderNo(String tradeOrderNo) {
        OrderExtends query = new OrderExtends();
        query.setOrderNo(tradeOrderNo);
        return orderExtendsMapper.selectOne(query);
    }

    public List<OrderItem> queryOrderItemByOrderNo(String tradeOrderNo) {
        OrderItem query = new OrderItem();
        query.setTradeOrderNo(tradeOrderNo);
        return orderItemMapper.select(query);
    }

    public List<OrderItemExtends> queryOrderItemExtendsByOrderNo(String tradeOrderNo) {
        OrderItemExtends query = new OrderItemExtends();
        query.setTradeOrderNo(tradeOrderNo);
        return orderItemExtendsMapper.select(query);
    }
    public List<OrderRefund> queryOrderRefundByOrderNo(String tradeOrderNo) {
        OrderRefund query = new OrderRefund();
        query.setTradeOrderNo(tradeOrderNo);
        return orderRefundMapper.select(query);
    }

    public TradeOrderVO queryOneOrder(TradeOrderQueryOneDTO tradeOrderQueryOneDTO) {
        TradeOrder query = BeanUtil.copyProperties(tradeOrderQueryOneDTO, TradeOrder.class);
        TradeOrder tradeOrder = tradeOrderMapper.selectOne(query);
        return buildTradeOrderVOS(List.of(tradeOrder)).get(0);
    }

    public List<TradeOrderVO> queryListOrder(TradeOrderQueryListDTO tradeOrderQueryListDTO) {
        /**
         * 以后再实现
         */
        return null;
    }

    public Map<String,String> queryOrderStatistics(TradeOrderQueryStatisticDTO TradeOrderQueryPageDTO) {
        TradeOrderQuery tradeOrderQuery = BeanUtil.copyProperties(TradeOrderQueryPageDTO, TradeOrderQuery.class);
        return tradeOrderMapper.selectStatisticsByQuery(tradeOrderQuery);
    }

    public TableDataInfo<OrderOperationLogVO> queryPageOperatorLog(TradeOrderLogQueryPageDTO tradeOrderLogQueryPageDTO) {
        PageUtil.startPage(tradeOrderLogQueryPageDTO);
        TradeOrderLogQuery tradeOrderLogQuery = new TradeOrderLogQuery();
        BeanUtil.copyProperties(tradeOrderLogQueryPageDTO, tradeOrderLogQuery);
        List<OrderOperationLog> orderOperationLogs = orderOperationLogMapper.selectByQuery(tradeOrderLogQuery);
        List<OrderOperationLogVO> TradeOrderVOS = BeanUtil.copyToList(orderOperationLogs, OrderOperationLogVO.class);

        PageInfo page = new PageInfo(orderOperationLogs);
        TableDataInfo<OrderOperationLogVO> tableData = new TableDataInfo<>();
        tableData.setTotal(page.getTotal());
        tableData.setRows(TradeOrderVOS);
        return tableData;
    }

    public List<OrderOperationLogVO> queryListOperatorLog(TradeOrderLogQueryPageDTO tradeOrderLogQueryPageDTO) {
        TradeOrderLogQuery tradeOrderLogQuery = new TradeOrderLogQuery();
        BeanUtil.copyProperties(tradeOrderLogQueryPageDTO, tradeOrderLogQuery);
        List<OrderOperationLog> orderOperationLogs = orderOperationLogMapper.selectByQuery(tradeOrderLogQuery);
        return BeanUtil.copyToList(orderOperationLogs, OrderOperationLogVO.class);
    }

    public void updateOrderStatusTo(BaseContext baseContext, EnumTradeOrderStatus status) {
        String tradeOrderNo = baseContext.getTradeOrderNo();

        TradeOrder tradeOrder = new TradeOrder();
        tradeOrder.setTradeOrderNo(tradeOrderNo);
        tradeOrder.setStatus(status.getCode());

        updateOrderByTradeOrder(tradeOrder,baseContext);
    }

    public void updatePaymentStatusTo(BaseContext baseContext, EnumOrderPaymentStatus status) {
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = new TradeOrder();
        tradeOrder.setTradeOrderNo(tradeOrderNo);
        tradeOrder.setPaymentStatus(status.getCode());
        updateOrderByTradeOrder(tradeOrder, baseContext);
    }
    public void updateOrderRefundStatusTo(BaseContext baseContext, EnumOrderRefundStatus status) {
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = new TradeOrder();
        tradeOrder.setTradeOrderNo(tradeOrderNo);
        tradeOrder.setRefundStatus(status.getCode());
        updateOrderByTradeOrder(tradeOrder, baseContext);
    }


    public List<String> getChildOrderNos(String currentOrderNo) {
        TradeOrder query = new TradeOrder();
        query.setParentNo(currentOrderNo);
        List<TradeOrder> select = tradeOrderMapper.select(query);
        if(CollUtil.isEmpty(select)){
            return new ArrayList<>();
        }
        return select.stream().map(TradeOrder::getTradeOrderNo).collect(Collectors.toList());
    }

    public void syncStatus(BaseContext baseContext, String status, String paymentStatus, String refundStatus) {
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = new TradeOrder();
        tradeOrder.setTradeOrderNo(tradeOrderNo);
        tradeOrder.setStatus(status);
        tradeOrder.setPaymentStatus(paymentStatus);
        tradeOrder.setRefundStatus(refundStatus);
        updateOrderByTradeOrder(tradeOrder, baseContext);
    }

    public void updateOrderFulfillmentStatusTo(BaseContext baseContext, EnumFulfillmentStatus enumFulfillmentStatus) {
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = new TradeOrder();
        tradeOrder.setTradeOrderNo(tradeOrderNo);
        tradeOrder.setFulfillmentStatus(enumFulfillmentStatus.getCode());
        updateOrderByTradeOrder(tradeOrder, baseContext);

    }

    public OrderStatusHistoryVO queryOrderStatusHistory(String tradeOrderNo, EnumTradeOrderStatus byCode) {

        OrderStatusHistory orderStatusHistory = new OrderStatusHistory();
        orderStatusHistory.setTradeOrderNo(tradeOrderNo);
        orderStatusHistory.setAfterStatus(byCode.getCode());
        List<OrderStatusHistory> orderStatusHistories = orderStatusHistoryMapper.select(orderStatusHistory);
        if(CollUtil.isNotEmpty(orderStatusHistories)){
            return BeanUtil.copyProperties(orderStatusHistories.get(0), OrderStatusHistoryVO.class);
        }
        return null;
    }

    public void insertOrderOperationLog(TradeOrder tradeOrder, BaseContext baseContext) {

        //order_operation_log更新
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setTradeOrderNo(tradeOrder.getTradeOrderNo());

        //todo
//        orderOperationLog.setBeforeData(JSONUtil.toJsonStr(beforeDataSnapshotEntity));
//        orderOperationLog.setAfterData(JSONUtil.toJsonStr(afterDataSnapshotEntity));
        orderOperationLog.setCreateUser(String.valueOf(baseContext.getOperatorUserId()));
        orderOperationLog.setUserId(tradeOrder.getUserId());
        orderOperationLog.setUserName(tradeOrder.getNickName());
        orderOperationLog.setOperatorUserId(baseContext.getOperatorUserId());
        orderOperationLog.setOperatorUserName(baseContext.getOperatorUserName());
        orderOperationLog.setOperatorTime(new Date());

        orderOperationLog.setNodeId(baseContext.getNodeId());
        orderOperationLog.setNodeName(baseContext.getNodeName());
        orderOperationLog.setChainId(baseContext.getChainId());
        orderOperationLog.setChainName(baseContext.getChainName());
        String traceId = TraceContext.traceId();
        orderOperationLog.setTraceId(traceId);
        orderOperationLogMapper.insertSelective(orderOperationLog);
    }

    public void updateOrderExtends(OrderExtends updateOrderExtends) {
        orderExtendsMapper.updateByPrimaryKeySelective(updateOrderExtends);
    }
}
