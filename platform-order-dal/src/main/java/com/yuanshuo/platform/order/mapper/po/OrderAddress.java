package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * 表名：order_address
 * 表注释：用户地址簿
*/
@Data
@Table(name = "order_address")
public class OrderAddress{
    /**
     * 主键ID（自增）
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 关联用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 关联订单编号
     */
    @Column(name = "trade_order_no")
    private String tradeOrderNo;

    /**
     * 收件关联code
     */
    @Column(name = "received_association_code")
    private String receivedAssociationCode;

    /**
     * 收件地址别名
     */
    @Column(name = "received_address_alias")
    private String receivedAddressAlias;

    /**
     * 收件人姓名
     */
    @Column(name = "received_name")
    private String receivedName;

    /**
     * 收件电话
     */
    @Column(name = "received_phone")
    private String receivedPhone;

    /**
     * 收件省份/直辖市
     */
    @Column(name = "received_province")
    private String receivedProvince;

    /**
     * 收件城市
     */
    @Column(name = "received_city")
    private String receivedCity;

    /**
     * 收件区/县
     */
    @Column(name = "received_district")
    private String receivedDistrict;

    /**
     * 收件乡/镇
     */
    @Column(name = "received_township")
    private String receivedTownship;

    /**
     * 收件详细街道地址
     */
    @Column(name = "received_street")
    private String receivedStreet;

    /**
     * 收件省份/直辖市code
     */
    @Column(name = "received_province_code")
    private String receivedProvinceCode;

    /**
     * 收件城市code
     */
    @Column(name = "received_city_code")
    private String receivedCityCode;

    /**
     * 收件区/县code
     */
    @Column(name = "received_district_code")
    private String receivedDistrictCode;

    /**
     * 收件乡/镇code
     */
    @Column(name = "received_township_code")
    private String receivedTownshipCode;

    @Column(name = "received_longitude")
    private Double receivedLongitude;

    @Column(name = "received_latitude")
    private Double receivedLatitude;

    /**
     * 发件地址别名
     */
    @Column(name = "send_association_code")
    private String sendAssociationCode;
    /**
     * 发件地址别名
     */
    @Column(name = "send_address_alias")
    private String sendAddressAlias;

    /**
     * 发件人姓名
     */
    @Column(name = "send_name")
    private String sendName;

    /**
     * 发件电话
     */
    @Column(name = "send_phone")
    private String sendPhone;

    /**
     * 发件省份/直辖市
     */
    @Column(name = "send_province")
    private String sendProvince;

    /**
     * 发件城市
     */
    @Column(name = "send_city")
    private String sendCity;

    /**
     * 发件区/县
     */
    @Column(name = "send_district")
    private String sendDistrict;

    /**
     * 发件乡/镇
     */
    @Column(name = "send_township")
    private String sendTownship;

    /**
     * 发件详细街道地址
     */
    @Column(name = "send_street")
    private String sendStreet;

    /**
     * 发件省份/直辖市code
     */
    @Column(name = "send_province_code")
    private String sendProvinceCode;

    /**
     * 发件城市code
     */
    @Column(name = "send_city_code")
    private String sendCityCode;

    /**
     * 发件区/县code
     */
    @Column(name = "send_district_code")
    private String sendDistrictCode;

    /**
     * 发件乡/镇code
     */
    @Column(name = "send_township_code")
    private String sendTownshipCode;

    @Column(name = "send_longitude")
    private Double sendLongitude;

    @Column(name = "send_latitude")
    private Double sendLatitude;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}