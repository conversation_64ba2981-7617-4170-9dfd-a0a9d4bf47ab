<flow>
    <!--包括订单总状态、支付状态、履约状态、退款状态的基础节点-->
    <!--订单总状态-->
    <!-- 订单状态为支付中-->
    <chain name="nodeUpdateOrderStatusToPaying">
        THEN(
            nodeUpdateOrderStatusTo.bind("status","paying")
        )
    </chain>
    <!-- 订单状态为履约中-->
    <chain name="nodeUpdateOrderStatusToFulfilling">
        THEN(
            nodeUpdateOrderStatusTo.bind("status","fulfilling")
        )
    </chain>
    <!-- 订单状态为已完成-->
    <chain name="nodeUpdateOrderStatusToCompleted">
        THEN(
            nodeUpdateOrderStatusTo.bind("status","completed")
        )
    </chain>
    <!-- 订单状态为取消中-->
    <chain name="nodeUpdateOrderStatusToCancelling">
        THEN(
            nodeUpdateOrderStatusTo.bind("status","cancelling")
        )
    </chain>
    <!-- 订单状态为已取消-->
    <chain name="nodeUpdateOrderStatusToCancelled">
        THEN(
            nodeUpdateOrderStatusTo.bind("status","cancelled")
        )
    </chain>
    <!-- 订单状态为退款中-->
    <chain name="nodeUpdateOrderStatusToRefunding">
        THEN(
            nodeUpdateOrderStatusTo.bind("status","refunding")
        )
    </chain>
    <!-- 订单状态为已关闭-->
    <chain name="nodeUpdateOrderStatusToClosed">
        THEN(
            nodeUpdateOrderStatusTo.bind("status","closed")
        )
    </chain>
    <!-- 订单支付状态更新 -->
    <!--订单支付状态更新为待支付-->
    <chain name="nodeUpdateOrderPaymentStatusToPendingPayment">
        THEN(
            nodeUpdatePaymentStatusTo.bind("status","pending_payment")
        )
    </chain>
    <!--订单支付状态更新为支付成功-->
    <chain name="nodeUpdateOrderPaymentStatusToPaid">
        THEN(
            nodeUpdateOrderToPaid
        )
    </chain>
    <!--订单支付状态更新为取消-->
    <chain name="nodeUpdateOrderPaymentStatusToCancelled">
        THEN(
            nodeUpdatePaymentStatusTo.bind("status","cancelled")
        )
    </chain>
    <!--订单支付状态更新为支付失败-->
    <chain name="nodeUpdateOrderPaymentStatusToFailed">
        THEN(
            nodeUpdatePaymentStatusTo.bind("status","failed")
        )
    </chain>
    <!-- 订单履约状态-->
    <!--订单履约状态更新为待派车-->
    <chain name="nodeUpdateFulfillmentStatusToDispatchPending">
        THEN(
            nodeUpdateOrderFulfillmentStatusTo.bind("fulfillmentStatus","dispatch_pending")
        )
    </chain>
    <!--订单履约状态更新为运输中-->
    <chain name="nodeUpdateFulfillmentStatusToTransiting">
        THEN(
            nodeUpdateOrderFulfillmentStatusTo.bind("fulfillmentStatus","transiting")
        )
    </chain>
    <!--订单履约状态更新为成功-->
    <chain name="nodeUpdateFulfillmentStatusToSuccess">
        THEN(
            nodeUpdateOrderFulfillmentStatusTo.bind("fulfillmentStatus","success")
        )
    </chain>
    <!--订单履约状态更新为失败-->
    <chain name="nodeUpdateFulfillmentStatusToFail">
        THEN(
            nodeUpdateOrderFulfillmentStatusTo.bind("fulfillmentStatus","fail")
        )
    </chain>

    <!-- 订单退款状态更新 -->
    <chain name="nodeUpdateOrderRefundStatusToRefundInitialize">
        THEN(
        nodeUpdateOrderRefundStatusTo.bind("status","refund_initialize")
        )
    </chain>
    <chain name="nodeUpdateOrderRefundStatusToUnRefunded">
        THEN(
            nodeUpdateOrderRefundStatusTo.bind("status","un_refunded")
        )
    </chain>
    <chain name="nodeUpdateOrderRefundStatusToRefunded">
        THEN(
            nodeUpdateOrderRefundStatusTo.bind("status","refunded")
        )
    </chain>
    <chain name="nodeUpdateOrderRefundStatusToRefunding">
        THEN(
            nodeUpdateOrderRefundStatusTo.bind("status","refunding")
        )
    </chain>
    <chain name="nodeUpdateOrderRefundStatusToRefundFailed">
        THEN(
        nodeUpdateOrderRefundStatusTo.bind("status","refund_failed")
        )
    </chain>

    <!-- 退款单状态更新 -->
    <chain name="nodeUpdateRefundOrderStatusToUnRefunded">
        THEN(
            nodeUpdateRefundOrderStatusTo.bind("status","un_refunded")
        )
    </chain>
    <chain name="nodeUpdateRefundOrderStatusToRefunding">
        THEN(
            nodeUpdateRefundOrderStatusTo.bind("status","refunding")
        )
    </chain>
    <chain name="nodeUpdateRefundOrderStatusToRefunded">
        THEN(
            nodeUpdateRefundOrderStatusTo.bind("status","refunded")
        )
    </chain>
    <chain name="nodeUpdateRefundOrderStatusToRefundFailed">
        THEN(
            nodeUpdateRefundOrderStatusTo.bind("status","refund_failed")
        )
    </chain>


    <!--同时更新父子订单状态-->
    <chain name="syncStatus">
        THEN(
            parentUpdateSyncChild,
            childUpdateSyncParent
        )
    </chain>
</flow>
