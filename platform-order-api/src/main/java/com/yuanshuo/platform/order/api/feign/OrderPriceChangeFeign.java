package com.yuanshuo.platform.order.api.feign;

import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.model.dto.OrderPriceChangeDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderPriceChangeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 订单改价服务接口
 */
@FeignClient(value = "platform-order")
public interface OrderPriceChangeFeign {
    
    /**
     * 订单改价申请
     */
    @RequestMapping(value = "/order/changeOrderPrice", method = RequestMethod.POST)
    R<Void> changeOrderPrice(@RequestBody OrderPriceChangeDTO priceChangeDTO);
    
    /**
     * 查询订单改价历史
     */
    @RequestMapping(value = "/order/getPriceChangeHistory", method = RequestMethod.GET)
    R<List<OrderPriceChangeVO>> getPriceChangeHistory(@RequestParam("tradeOrderNo") String tradeOrderNo);
    
    /**
     * 获取订单最新改价记录
     */
    @RequestMapping(value = "/order/getLatestPriceChange", method = RequestMethod.GET)
    R<OrderPriceChangeVO> getLatestPriceChange(@RequestParam("tradeOrderNo") String tradeOrderNo);
}