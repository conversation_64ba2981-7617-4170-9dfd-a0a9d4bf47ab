package com.yuanshuo.platform.order.api.feign;

import com.github.pagehelper.PageInfo;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.platform.order.api.model.dto.query.AdministrativeDivisionQueryDTO;
import com.yuanshuo.platform.order.api.model.vo.AdministrativeDivisionVO;
import com.yuanshuo.common.entity.web.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 行政区划Feign接口
 */
@FeignClient(name = "platform-order")
public interface AdministrativeDivisionFeign {

    /**
     * 模糊查询城市
     * @param queryDTO 查询条件
     * @return 分页城市列表
     */
    @PostMapping("/administrative-division/search")
    R<TableDataInfo<AdministrativeDivisionVO>> searchCities(@RequestBody AdministrativeDivisionQueryDTO queryDTO);

    /**
     * 根据编码转换为中文名
     * @param queryDTO 查询条件（包含编码列表）
     * @return 编码与中文名的映射关系
     */
    @PostMapping("/code-to-name")
    Map<String, String> convertCodeToName(@RequestBody AdministrativeDivisionQueryDTO queryDTO);
}