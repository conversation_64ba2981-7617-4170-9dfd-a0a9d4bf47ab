package com.yuanshuo.platform.order.mapper;

import com.yuanshuo.platform.order.mapper.po.AdministrativeDivision;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行政区划Mapper
 */
@Mapper
public interface AdministrativeDivisionMapper {

    /**
     * 根据编码查询行政区划
     * @param code 行政区编码
     * @return 行政区划信息
     */
    AdministrativeDivision selectByCode(@Param("code") String code);

    /**
     * 模糊查询城市（按名称）
     * @param name 城市名称关键字
     * @param level 级别（可选）
     * @return 城市列表
     */
    List<AdministrativeDivision> selectByNameLike(@Param("name") String name, @Param("level") String level);

    /**
     * 批量根据编码查询行政区划
     * @param codes 行政区编码列表
     * @return 行政区划列表
     */
    List<AdministrativeDivision> selectByCodes(@Param("codes") List<String> codes);

    /**
     * 查询所有行政区划（支持分页）
     * @param level 级别（可选）
     * @return 行政区划列表
     */
    List<AdministrativeDivision> selectAll(@Param("level") String level);
}