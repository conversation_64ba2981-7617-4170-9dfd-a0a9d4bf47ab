package com.yuanshuo.platform.order.flow.context.order;

import cn.hutool.core.bean.BeanUtil;
import com.yuanshuo.platform.order.api.model.dto.TradeOrderDTO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.function.Supplier;

@Data
@NoArgsConstructor
public class CreateOrderContext extends BaseContext {
    /**
     * 创建订单的入参DTO
     */
    private TradeOrderDTO tradeOrderDTO;
    /**
     * 创建完订单的返回参数
     */
    private TradeOrderVO tradeOrderVO;

    /**
     * orderNo生成器
     */
    private Supplier<String> generateOrderNo;


    /**
     *  子订单orderNo生成器
     */
    private Supplier<String> childGenerateOrderNo;

    /**
     * itemOrderNo生成器
     */
    private Supplier<String> generateItemOrderNo;

    /**
     * 从BaseContext构造CreateOrderContext，继承BaseContext的所有属性值
     * @param baseContext 基础上下文
     */
    public CreateOrderContext(BaseContext baseContext) {
        BeanUtil.copyProperties(baseContext, this);
    }

    /**
     * 从BaseContext构造CreateOrderContext，并设置tradeOrderDTO
     * @param baseContext 基础上下文
     * @param tradeOrderDTO 交易订单DTO
     */
    public CreateOrderContext(BaseContext baseContext, TradeOrderDTO tradeOrderDTO) {
        BeanUtil.copyProperties(baseContext, this);
        this.tradeOrderDTO = tradeOrderDTO;
    }

}
