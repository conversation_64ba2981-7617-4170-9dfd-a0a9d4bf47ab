package com.yuanshuo.platform.order.api.enums;

public enum EnumOrderItemType {
    PRODUCT("product", "商品"),
    SERVICE("service", "服务"),
    OTHER("other", "其他");

    private String code;
    private String msg;

    EnumOrderItemType(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
