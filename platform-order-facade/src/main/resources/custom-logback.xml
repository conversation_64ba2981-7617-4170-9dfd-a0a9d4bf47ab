<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <property name="LOG_APP_NAME" value="${APP_NAME}"/>
    <property name="ASYNC_DISCARDING_THRESHOLD" value="${log.async.discarding-threshold:-0}"/>
    <property name="ASYNC_LOG_QUEUE_SIZE" value="${log.async.queue-size:-256}"/>

    <!-- 添加 SkyWalking trace id 到日志格式中 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%tid] [%thread] %-5level %logger{36} - %msg%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${LOG_PATTERN}</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <queueSize>${ASYNC_LOG_QUEUE_SIZE}</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="STDOUT"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="ASYNC_STDOUT"/>
    </root>
</configuration>