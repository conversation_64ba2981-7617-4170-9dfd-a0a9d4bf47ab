package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * 表名：order_item_extends
 * 表注释：订单扩展表
*/
@Data
@Table(name = "order_item_extends")
public class OrderItemExtends {
    /**
     * 主键ID（自增）
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 关联订单表id
     */
    @Column(name = "trade_order_no")
    private String tradeOrderNo;

    /**
     * 关联订单表详情表id
     */
    @Column(name = "order_item_no")
    private String orderItemNo;

    /**
     * 扩展数据
     */
    @Column(name = "ext_data")
    private String extData;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}