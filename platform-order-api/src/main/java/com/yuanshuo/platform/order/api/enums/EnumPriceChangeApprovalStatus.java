package com.yuanshuo.platform.order.api.enums;

/**
 * 订单改价审核状态枚举
 */
public enum EnumPriceChangeApprovalStatus {
    
    /**
     * 自动通过
     */
    AUTO_APPROVED("auto_approved", "自动通过"),
    
    /**
     * 人工通过
     */
    MANUAL_APPROVED("manual_approved", "人工通过"),
    
    /**
     * 拒绝
     */
    REJECTED("rejected", "拒绝");
    
    private final String code;
    private final String desc;
    
    EnumPriceChangeApprovalStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static EnumPriceChangeApprovalStatus getByCode(String code) {
        for (EnumPriceChangeApprovalStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}