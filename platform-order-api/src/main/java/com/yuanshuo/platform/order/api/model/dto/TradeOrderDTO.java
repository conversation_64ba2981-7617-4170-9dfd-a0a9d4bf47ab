package com.yuanshuo.platform.order.api.model.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 订单入口入参
 */
@Data
public class TradeOrderDTO extends BaseDTO{

    /**
     * 订单类型,见枚举enum EnumOrderType,目前只有一种code:little_seller(销售小工具)
     */
    @NotEmpty
    private String orderType;

    /**
     * 业务类型,见枚举EnumBizType,目前只有一种code:transport(运输)
     */
    @NotEmpty
    private String bizType;


    /**
     * 业务子类型,如无人车,见枚举EnumSubBizType,目前只有一种code:driverless_car(无人车)
     */
    @NotEmpty
    private String subBizType;

    /**
     * 渠道类型,支付宝小程序/微信小程序等等,见枚举EnumChannelType,目前只有一种code:wechat_mini_program
     */
    @NotEmpty
    private String channelType;

    /**
     * 用户ID
     */
    @NotNull
    private Long userId;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 联系电话
     */
    private String userPhone;

    /**
     * 总金额（主订单有效）
     */
    @NotNull
    private Integer totalAmount;

    /**
     * 总优惠金额
     */
    private Integer totalDiscount=0;
    


    /**
     * 订单扩展数据,json格式,不会做特殊处理
     */
    private String orderExtData;

    /**
     * 订单超时时间
     */
    private Date orderExpireTime;

    /**
     * 商品详情
     */
    @NotNull
    @Valid
    private List<TradeOrderItemDTO> tradeOrderItems;

    /**
     * 收货地址
     */
    private OrderAddressDTO orderAddress;

    /**
     * 预定履约时间
     */
    private Date reserveTime;

    /**
     * 是否实时单 1-是 0-否
     */
    private Boolean actualOrderType;

    /**
     * 支付渠道
     */
    private String payChannel;

    /**
     * 子订单列表
     */
    private List<TradeOrderDTO> childOrders;
}
