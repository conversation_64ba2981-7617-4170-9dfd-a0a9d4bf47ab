<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderStatusHistoryMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderStatusHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="operator_user_id" jdbcType="VARCHAR" property="operatorUserId" />
    <result column="operator_user_name" jdbcType="VARCHAR" property="operatorUserName" />
    <result column="operator_time" jdbcType="TIMESTAMP" property="operatorTime" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="chain_id" jdbcType="VARCHAR" property="chainId" />
    <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
    <result column="before_status" jdbcType="VARCHAR" property="beforeStatus" />
    <result column="after_status" jdbcType="VARCHAR" property="afterStatus" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>