package com.yuanshuo.platform.order.mapper;

import com.yuanshuo.platform.order.mapper.po.TradeOrder;
import com.yuanshuo.platform.order.mapper.query.TradeOrderQuery;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface TradeOrderMapper extends Mapper<TradeOrder> {
    List<TradeOrder> selectByQuery(TradeOrderQuery tradeOrderQuery);

    Map<String,String> selectStatisticsByQuery(TradeOrderQuery tradeOrderQuery);
}