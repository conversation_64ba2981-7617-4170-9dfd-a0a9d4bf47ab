package com.yuanshuo.platform.order.mapper;

import com.yuanshuo.platform.order.mapper.po.OrderPriceChange;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 订单改价记录Mapper
 */
public interface OrderPriceChangeMapper extends Mapper<OrderPriceChange> {
    
    /**
     * 根据订单号查询改价记录
     */
    List<OrderPriceChange> selectByTradeOrderNo(@Param("tradeOrderNo") String tradeOrderNo);
    
    /**
     * 根据订单号查询最新的改价记录
     */
    OrderPriceChange selectLatestByTradeOrderNo(@Param("tradeOrderNo") String tradeOrderNo);

    /**
     * 根据订单号和资金处理状态查询改价记录
     */
    List<OrderPriceChange> selectByTradeOrderNoAndFundProcessStatusIn(@Param("tradeOrderNo") String tradeOrderNo,
                                                                        @Param("fundProcessStatuses") List<String> fundProcessStatuses);
    
    /**
     * 根据改价单号查询改价记录
     */
    OrderPriceChange selectByPriceChangeNo(@Param("priceChangeNo") String priceChangeNo);
}