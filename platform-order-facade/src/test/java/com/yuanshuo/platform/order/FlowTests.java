package com.yuanshuo.platform.order;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.gson.Gson;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.enums.*;
import com.yuanshuo.platform.order.api.feign.TradeOrderFeign;
import com.yuanshuo.platform.order.api.model.dto.*;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.mapper.po.TradeOrder;
import com.yuanshuo.platform.order.server.TradeOrderService;
import com.yuanshuo.platform.order.server.flow.TradeFlowService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest(properties = {
        "spring.profiles.active=local"
})
public class FlowTests {
    @Autowired
    private TradeFlowService tradeFlowService;
    @Resource
    private FlowExecutor flowExecutor;
    @Resource
    TradeOrderService tradeOrderService;
    @Autowired
    private TradeOrderFeign traceOrderFeign;

    @Test
    public void test()
    {
        TradeOrderVO order = tradeFlowService.createOrder(MiniSellerTradeFeignTests.createTestTradeOrderDTO());
        System.out.println(order);
    }

    @Test
    public void test2()
    {
        CallbackOrderAfterPayCancelDTO closeTradeOrderDTO = new CallbackOrderAfterPayCancelDTO();
        closeTradeOrderDTO.setTradeOrderNo("2025051919365397050");
        closeTradeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        closeTradeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayClose(closeTradeOrderDTO);
//        tradeFlowService.cancelOrderByOperations(closeTradeOrderDTO);
//        tradeFlowService.cancelOrderForTimeOut(closeTradeOrderDTO);

    }

    @Test
    public void test3() {
        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo("2025051915100376934");
        orderRefundDTO.setRefundAmount(150);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);


    }
    @Test
    public void test4()
    {
        TradeOrder record = new TradeOrder();
        record.setTradeOrderNo("2025051514465046957");
        record.setStatus(EnumTradeOrderStatus.PAYING.getCode());
        tradeOrderService.updateOrderByTradeOrder(record,new BaseContext());
        BaseContext cancelOrderContext = new BaseContext();
        cancelOrderContext.setTradeOrderNo("2025051514465046957");
        LiteflowResponse response = flowExecutor.execute2Resp("userCancelOrderForTradeLittleSeller", null, cancelOrderContext);

    }


    @Test
    public void test5()
    {
        // 订单主体信息
        TradeOrderDTO tradeOrderDTO = new TradeOrderDTO();
        tradeOrderDTO.setOrderType(EnumOrderType.APP_PLACE_ORDER.getCode());
        tradeOrderDTO.setBizType(EnumBizType.TRANSPORT.getCode());
        tradeOrderDTO.setChannelType(EnumChannelType.ANDROID_APP.getCode());
        tradeOrderDTO.setSubBizType(EnumSubBizType.DRIVERLESS_CAR.getCode());
        tradeOrderDTO.setUserId(158222L);
        tradeOrderDTO.setNickName("sss");
        tradeOrderDTO.setUserPhone("12345678941");
        tradeOrderDTO.setTotalAmount(200);
        tradeOrderDTO.setOrderExpireTime(DateUtil.offset(new Date(), DateField.MINUTE, 15));
        tradeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        tradeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeOrderDTO.setReserveTime(DateUtil.offset(new Date(), DateField.MINUTE, 60));
        //主订单地址 装货点至卸货点
        OrderAddressDTO orderAddress = MiniSellerTradeFeignTests.createTestOrderAddressDTO();
        tradeOrderDTO.setOrderAddress(orderAddress);

        List<TradeOrderDTO> childOrders = new ArrayList<>();
        //途径点信息  每一个途经点是一个子订单
        TradeOrderDTO childTradeOrderOneDTO = new TradeOrderDTO();

        childTradeOrderOneDTO.setOrderType(EnumOrderType.APP_PLACE_ORDER.getCode());
        childTradeOrderOneDTO.setBizType(EnumBizType.TRANSPORT.getCode());
        childTradeOrderOneDTO.setChannelType(EnumChannelType.ANDROID_APP.getCode());
        childTradeOrderOneDTO.setSubBizType(EnumSubBizType.DRIVERLESS_CAR.getCode());
        childTradeOrderOneDTO.setUserId(158222L);
        childTradeOrderOneDTO.setNickName("sss");
        childTradeOrderOneDTO.setUserPhone("12345678941");
        childTradeOrderOneDTO.setOrderExpireTime(DateUtil.offset(new Date(), DateField.MINUTE, 15));
        childTradeOrderOneDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        childTradeOrderOneDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
//
//        //装货点信息  第一为装货点 中间为途经点
        OrderAddressDTO childOrderAddressDTOOne = new OrderAddressDTO();
        childOrderAddressDTOOne.setSendAssociationCode(orderAddress.getSendAssociationCode());
        childOrderAddressDTOOne.setSendPhone(orderAddress.getSendPhone());
        childOrderAddressDTOOne.setSendName(orderAddress.getSendName());
        childOrderAddressDTOOne.setSendCity(orderAddress.getSendCity());
        childOrderAddressDTOOne.setSendCityCode(orderAddress.getSendCityCode());
        childOrderAddressDTOOne.setSendProvince(orderAddress.getSendProvince());
        childOrderAddressDTOOne.setSendProvinceCode(orderAddress.getSendProvinceCode());
        childOrderAddressDTOOne.setSendAddressAlias(orderAddress.getSendAddressAlias());
        childOrderAddressDTOOne.setSendStreet(orderAddress.getSendStreet());
        childOrderAddressDTOOne.setSendDistrict(orderAddress.getSendDistrict());
        childOrderAddressDTOOne.setSendDistrictCode(orderAddress.getSendDistrictCode());
        childOrderAddressDTOOne.setSendTownship(orderAddress.getSendTownship());
        childOrderAddressDTOOne.setSendTownshipCode(orderAddress.getSendTownshipCode());
        childOrderAddressDTOOne.setSendLongitude(orderAddress.getSendLongitude());
        childOrderAddressDTOOne.setSendLatitude(orderAddress.getSendLatitude());


//
//        //卸货点信息  最后一个为卸货点 中间为途经点
        childOrderAddressDTOOne.setReceivedAssociationCode("66666");
        childOrderAddressDTOOne.setReceivedPhone("18945673214");
        childOrderAddressDTOOne.setReceivedName("王五");
        childOrderAddressDTOOne.setReceivedProvince("江苏省");
        childOrderAddressDTOOne.setReceivedProvinceCode("210101");

        childOrderAddressDTOOne.setReceivedCity("南京市");
        childOrderAddressDTOOne.setReceivedCityCode("210110");

        childOrderAddressDTOOne.setReceivedDistrict("秦淮区");
        childOrderAddressDTOOne.setReceivedDistrictCode("110108");

        childOrderAddressDTOOne.setReceivedTownship("中华路街道");
        childOrderAddressDTOOne.setReceivedTownshipCode("110108001");

        childOrderAddressDTOOne.setReceivedAddressAlias("南南南南");
        childOrderAddressDTOOne.setReceivedStreet("中华路街道1号");


        childOrderAddressDTOOne.setReceivedLongitude(116.3265);
        childOrderAddressDTOOne.setReceivedLatitude(39.9991);

        childTradeOrderOneDTO.setOrderAddress(childOrderAddressDTOOne);
        childOrders.add(childTradeOrderOneDTO);

        TradeOrderDTO childTradeOrderTwoDTO = new TradeOrderDTO();

        childTradeOrderTwoDTO.setOrderType(EnumOrderType.APP_PLACE_ORDER.getCode());
        childTradeOrderTwoDTO.setBizType(EnumBizType.TRANSPORT.getCode());
        childTradeOrderTwoDTO.setChannelType(EnumChannelType.ANDROID_APP.getCode());
        childTradeOrderTwoDTO.setSubBizType(EnumSubBizType.DRIVERLESS_CAR.getCode());
        childTradeOrderTwoDTO.setUserId(158222L);
        childTradeOrderTwoDTO.setNickName("sss");
        childTradeOrderTwoDTO.setUserPhone("12345678941");
        childTradeOrderTwoDTO.setOrderExpireTime(DateUtil.offset(new Date(), DateField.MINUTE, 15));
        childTradeOrderTwoDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        childTradeOrderTwoDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());


        OrderAddressDTO childOrderAddressDTOTwo = new OrderAddressDTO();
        childOrderAddressDTOTwo.setSendAssociationCode(childOrderAddressDTOOne.getReceivedAssociationCode());
        childOrderAddressDTOTwo.setSendPhone(childOrderAddressDTOOne.getReceivedPhone());
        childOrderAddressDTOTwo.setSendName(childOrderAddressDTOOne.getReceivedName());
        childOrderAddressDTOTwo.setSendCity(childOrderAddressDTOOne.getReceivedCity());
        childOrderAddressDTOTwo.setSendCityCode(childOrderAddressDTOOne.getReceivedCityCode());
        childOrderAddressDTOTwo.setSendProvince(childOrderAddressDTOOne.getReceivedProvince());
        childOrderAddressDTOTwo.setSendProvinceCode(childOrderAddressDTOOne.getReceivedProvinceCode());
        childOrderAddressDTOTwo.setSendAddressAlias(childOrderAddressDTOOne.getReceivedAddressAlias());
        childOrderAddressDTOTwo.setSendStreet(childOrderAddressDTOOne.getReceivedStreet());
        childOrderAddressDTOTwo.setSendDistrict(childOrderAddressDTOOne.getReceivedDistrict());
        childOrderAddressDTOTwo.setSendDistrictCode(childOrderAddressDTOOne.getReceivedDistrictCode());
        childOrderAddressDTOTwo.setSendTownship(childOrderAddressDTOOne.getReceivedTownship());
        childOrderAddressDTOTwo.setSendTownshipCode(childOrderAddressDTOOne.getReceivedTownshipCode());
        childOrderAddressDTOTwo.setSendLongitude(childOrderAddressDTOOne.getReceivedLongitude());
        childOrderAddressDTOTwo.setSendLatitude(childOrderAddressDTOOne.getReceivedLatitude());

//        //卸货点信息  最后一个为卸货点 中间为途经点
        childOrderAddressDTOTwo.setReceivedAssociationCode(orderAddress.getReceivedAssociationCode());
        childOrderAddressDTOTwo.setReceivedPhone(orderAddress.getReceivedPhone());
        childOrderAddressDTOTwo.setReceivedName(orderAddress.getReceivedName());
        childOrderAddressDTOTwo.setReceivedProvince(orderAddress.getReceivedProvince());
        childOrderAddressDTOTwo.setReceivedProvinceCode(orderAddress.getReceivedProvinceCode());
        childOrderAddressDTOTwo.setReceivedCity(orderAddress.getReceivedCity());
        childOrderAddressDTOTwo.setReceivedCityCode(orderAddress.getReceivedCityCode());
        childOrderAddressDTOTwo.setReceivedDistrict(orderAddress.getReceivedDistrict());
        childOrderAddressDTOTwo.setReceivedDistrictCode(orderAddress.getReceivedDistrictCode());
        childOrderAddressDTOTwo.setReceivedTownship(orderAddress.getReceivedTownship());
        childOrderAddressDTOTwo.setReceivedTownshipCode(orderAddress.getReceivedTownshipCode());
        childOrderAddressDTOTwo.setReceivedAddressAlias(orderAddress.getReceivedAddressAlias());
        childOrderAddressDTOTwo.setReceivedStreet(orderAddress.getReceivedStreet());
        childOrderAddressDTOTwo.setReceivedLongitude(orderAddress.getReceivedLongitude());
        childOrderAddressDTOTwo.setReceivedLatitude(orderAddress.getReceivedLatitude());

        childTradeOrderTwoDTO.setOrderAddress(childOrderAddressDTOTwo);

        childOrders.add(childTradeOrderTwoDTO);
        tradeOrderDTO.setChildOrders(childOrders);

//        //app下单中商品就是各种服务费
        List<TradeOrderItemDTO> tradeOrderItems = new ArrayList<>();


        TradeOrderItemDTO tradeOrderItemDTO = new TradeOrderItemDTO();
        tradeOrderItemDTO.setItemName("起步价");
        tradeOrderItemDTO.setItemType(EnumOrderItemType.SERVICE.getCode());
        tradeOrderItemDTO.setQuantity(5); //5公里
        tradeOrderItemDTO.setPrice(10);
        tradeOrderItemDTO.setItemCode("001");

        //扩展信息
        Map<String, Object> orderExtData = new HashMap<>();
        orderExtData.put("goodsCategory", "人民币");
        orderExtData.put("goodsUnit", "元");
        orderExtData.put("goodsPic", "[\"1ac772701b9844ceb3da6c6396e8787d\",\"07db3b9acee8452c872a7f09d95f6b15\"]");
        tradeOrderItemDTO.setItemExtData(JSON.toJSONString(orderExtData));

        tradeOrderItems.add(tradeOrderItemDTO);

        TradeOrderItemDTO tradeOrderItemDTOOne = new TradeOrderItemDTO();
        tradeOrderItemDTOOne.setItemName("超公里费");
        tradeOrderItemDTOOne.setItemType(EnumOrderItemType.SERVICE.getCode());
        tradeOrderItemDTOOne.setQuantity(5); //5公里
        tradeOrderItemDTOOne.setPrice(20);
        tradeOrderItemDTOOne.setItemCode("002");

        tradeOrderItems.add(tradeOrderItemDTOOne);


        TradeOrderItemDTO tradeOrderItemDTOTwo = new TradeOrderItemDTO();
        tradeOrderItemDTOTwo.setItemName("等待费");
        tradeOrderItemDTOTwo.setItemType(EnumOrderItemType.SERVICE.getCode());
        tradeOrderItemDTOTwo.setQuantity(5); //5分钟
        tradeOrderItemDTOTwo.setPrice(10);
        tradeOrderItemDTOTwo.setItemCode("003");

        tradeOrderItems.add(tradeOrderItemDTOTwo);

        tradeOrderDTO.setTradeOrderItems(tradeOrderItems);


        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);


        // 创建Gson实例
        Gson gson = new Gson();

        // 将对象转换为JSON字符串
        String json = gson.toJson(order.getData());
        System.out.println(json);
        System.out.println(JSON.toJSONString(order.getData()));

    }

}
