package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

/**
 * 行政区划VO
 */
@Data
public class AdministrativeDivisionVO {
    
    /**
     * 主键
     */
    private Long id;

    /**
     * 国标编码
     */
    private String code;

    /**
     * 行政区名称
     */
    private String name;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 上级行政区ID
     */
    private String parentCode;

    /**
     * 级别：province=省 city=市 district=区/县 street=镇
     */
    private String level;
}