package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import java.util.Date;

/**
 * 订单改价记录表
 */
@Data
@Table(name = "order_price_change")
public class OrderPriceChange {
    /**
     * 主键ID（自增）
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 订单号
     */
    @Column(name = "trade_order_no")
    private String tradeOrderNo;
    
    /**
     * 改价单号
     */
    @Column(name = "price_change_no")
    private String priceChangeNo;

    /**
     * 改价类型：before_payment-支付前改价，after_payment-支付后改价
     */
    @Column(name = "change_type")
    private String changeType;

    /**
     * 改价时支付状态
     */
    @Column(name = "payment_status")
    private String paymentStatus;

    /**
     * 改价前总金额（单位：分）
     */
    @Column(name = "before_total_amount")
    private Integer beforeTotalAmount;

    /**
     * 改价前总优惠金额（单位：分）
     */
    @Column(name = "before_total_discount")
    private Integer beforeTotalDiscount;

    /**
     * 改价前总应付金额（单位：分）
     */
    @Column(name = "before_total_payable")
    private Integer beforeTotalPayable;

    /**
     * 改价后总金额（单位：分）
     */
    @Column(name = "after_total_amount")
    private Integer afterTotalAmount;

    /**
     * 改价后总优惠金额（单位：分）
     */
    @Column(name = "after_total_discount")
    private Integer afterTotalDiscount;
    
    /**
     * 改价后总应付金额（单位：分）
     */
    @Column(name = "after_total_payable")
    private Integer afterTotalPayable;

    /**
     * 金额差额（单位：分，正数为涨价，负数为降价）
     */
    @Column(name = "amount_difference")
    private Integer amountDifference;

    /**
     * 改价原因
     */
    @Column(name = "change_reason")
    private String changeReason;

    /**
     * 改价状态：approved-已通过（自动审核）
     */
    @Column(name = "change_status")
    private String changeStatus;

    /**
     * 审核状态：auto_approved-自动通过，manual_approved-人工通过，rejected-拒绝
     */
    @Column(name = "approval_status")
    private String approvalStatus;

    /**
     * 审核人ID
     */
    @Column(name = "approver_id")
    private Long approverId;

    /**
     * 审核人姓名
     */
    @Column(name = "approver_name")
    private String approverName;

    /**
     * 审核时间
     */
    @Column(name = "approval_time")
    private Date approvalTime;

    /**
     * 审核备注
     */
    @Column(name = "approval_remark")
    private String approvalRemark;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 资金处理状态：pending-待处理，processing-处理中，completed-已完成，failed-失败
     */
    @Column(name = "fund_process_status")
    private String fundProcessStatus;

    /**
     * 资金处理类型：none-无需处理，refund-退款，supplement-补款
     */
    @Column(name = "fund_process_type")
    private String fundProcessType;

    /**
     * 资金处理金额（单位：分）
     */
    @Column(name = "fund_process_amount")
    private Integer fundProcessAmount;

    /**
     * 资金处理单号（退款单号或补款单号）
     */
    @Column(name = "fund_process_no")
    private String fundProcessNo;

    /**
     * 资金处理完成时间
     */
    @Column(name = "fund_process_time")
    private Date fundProcessTime;

    /**
     * 资金处理备注
     */
    @Column(name = "fund_process_remark")
    private String fundProcessRemark;
}