package com.yuanshuo.platform.order.rpc.impl;

import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.feign.OrderPriceChangeFeign;
import com.yuanshuo.platform.order.api.model.dto.OrderPriceChangeDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderPriceChangeVO;
import com.yuanshuo.platform.order.server.OrderPriceChangeService;
import com.yuanshuo.platform.order.server.flow.PriceChangeFlowService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单改价服务实现
 */
@RestController
@Slf4j
public class OrderPriceChangeFeignImpl implements OrderPriceChangeFeign {
    
    @Autowired
    private PriceChangeFlowService priceChangeFlowService ;
    @Autowired
    private OrderPriceChangeService orderPriceChangeService ;
    @Override
    public R<Void> changeOrderPrice(@Valid OrderPriceChangeDTO priceChangeDTO) {
        log.info("changeOrderPrice - input: {}", priceChangeDTO);
          priceChangeFlowService.createOrder(priceChangeDTO);
            log.info("changeOrderPrice - success for order: {}", priceChangeDTO.getTradeOrderNo());
            return R.success();
            
    }
    
    @Override
    public R<List<OrderPriceChangeVO>> getPriceChangeHistory(String tradeOrderNo) {
        log.info("getPriceChangeHistory - input: {}", tradeOrderNo);
            List<OrderPriceChangeVO> historyList = orderPriceChangeService.getPriceChangeHistory(tradeOrderNo);
            log.info("getPriceChangeHistory - output: {} records", historyList.size());
            return R.success(historyList);
            
    }
    
    @Override
    public R<OrderPriceChangeVO> getLatestPriceChange(String tradeOrderNo) {
        log.info("getLatestPriceChange - input: {}", tradeOrderNo);
            OrderPriceChangeVO latestChange = orderPriceChangeService.getLatestPriceChange(tradeOrderNo);
            log.info("getLatestPriceChange - output: {}", latestChange);
            return R.success(latestChange);
            
    }
}