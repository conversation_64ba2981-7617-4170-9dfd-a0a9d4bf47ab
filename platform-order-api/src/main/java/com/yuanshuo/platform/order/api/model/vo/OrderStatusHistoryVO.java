package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

import java.util.Date;

@Data
public class OrderStatusHistoryVO {
    /**
     * 主键ID（自增）
     */
    private Long id;

    /**
     * 订单编号
     */
    private String tradeOrderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称（冗余字段）
     */
    private String userName;

    /**
     * 操作人ID
     */
    private Long operatorUserId;

    /**
     * 操作人名称（
     */
    private String operatorUserName;

    /**
     * 操作时间
     */
    private Date operatorTime;


    /**
     * 操作节点id
     */
    private String nodeId;

    /**
     * 操作节点名称
     */
    private String nodeName;

    /**
     * 操作流程的ID
     */
    private String chainId;

    /**
     * 操作流程的名称
     */
    private String chainName;

    /**
     * 操作前订单状态
     */
    private String beforeStatus;

    /**
     * 操作后订单状态
     */
    private String afterStatus;

    /**
     * 全链路追踪ID
     */
    private String traceId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    private String createUser;

    private Date createTime;

    private Date updateTime;
}
