package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * 表名：order_flow
*/
@Data
@Table(name = "order_flow")
public class OrderFlow {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 创建订单时走的流程
     */
    @Column(name = "create_order")
    private String createOrder;

    /**
     * 支付服务支付单创建
     */
    @Column(name = "callback_pay_create")
    private String callbackPayCreate;

    /**
     * 支付服务支付成功
     */
    @Column(name = "callback_pay_success")
    private String callbackPaySuccess;

    /**
     * 支付服务支付失败
     */
    @Column(name = "callback_pay_fail")
    private String callbackPayFail;


    /**
     * 用户取消订单流程
     */
    @Column(name = "user_cancel_order")
    private String userCancelOrder;

    /**
     * 运营取消订单流程
     */
    @Column(name = "operations_cancel_order")
    private String operationsCancelOrder;

    /**
     * 超时自动取消订单
     */
    @Column(name = "time_out_cancel_order")
    private String timeOutCancelOrder;
    /**
     * 支付服务关闭
     */
    @Column(name = "callback_pay_close")
    private String callbackPayClose;


    /**
     * 创建退款单的流程
     */
    @Column(name = "create_refund_order")
    private String createRefundOrder;
    /**
     * 支付服务退款单创建
     */
    @Column(name = "callback_pay_refund_create")
    private String callbackPayRefundCreate;

    /**
     * 支付服务退款成功
     */
    @Column(name = "callback_pay_refund_success")
    private String callbackPayRefundSuccess;

    /**
     * 支付服务退款失败
     */
    @Column(name = "callback_pay_refund_fail")
    private String callbackPayRefundFail;


    /**
     * 履约服务运输单创建流程
     */
    @Column(name = "callback_fulfillment_create")
    private String callbackFulfillmentCreate;
    /**
     * 履约服务运输流程
     */
    @Column(name = "callback_fulfillment_transport")
    private String callbackFulfillmentTransport;

    /**
     * 履约服务完成流程
     */
    @Column(name = "callback_fulfillment_complete")
    private String callbackFulfillmentComplete;
    /**
     * 订单表对应的order_type
     */
    @Column(name = "order_type")
    private String orderType;

    @Column(name = "is_delete")
    private Integer isDelete;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}