package com.yuanshuo.platform.order.api.enums;

public enum EnumOperatorType {

    TRADE_LITTLE_SELLER(-1L, "销售小工具","销售小工具服务系统操作人"),
    PLATFORM_PAY(-2L, "支付服务","支付服务系统操作人"),
    PLATFORM_ORDER(-3L, "订单服务","订单服务系统操作人"),
    FULFILLMENT(-4L, "履约服务","履约服务系统操作人"),
    OTHER(-100L, "其他服务","其他服务系统操作人");

    private Long code;
    private String name;
    private String msg;

    EnumOperatorType(Long code,String name, String msg) {
        this.code = code;
        this.name = name;
        this.msg = msg;
    }

    public Long getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
    public String getMsg() {
        return msg;
    }

}
