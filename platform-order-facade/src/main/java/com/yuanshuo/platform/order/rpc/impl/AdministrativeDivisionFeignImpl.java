package com.yuanshuo.platform.order.rpc.impl;

import com.github.pagehelper.PageInfo;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.platform.order.api.feign.AdministrativeDivisionFeign;
import com.yuanshuo.platform.order.api.model.dto.query.AdministrativeDivisionQueryDTO;
import com.yuanshuo.platform.order.api.model.vo.AdministrativeDivisionVO;
import com.yuanshuo.platform.order.server.AdministrativeDivisionService;
import com.yuanshuo.common.entity.web.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 行政区划Feign接口实现
 */
@Slf4j
@RestController
public class AdministrativeDivisionFeignImpl implements AdministrativeDivisionFeign {
    
    @Autowired
    private AdministrativeDivisionService administrativeDivisionService;
    
    @Override
    public R<TableDataInfo<AdministrativeDivisionVO>> searchCities(AdministrativeDivisionQueryDTO queryDTO) {
        try {
            TableDataInfo<AdministrativeDivisionVO> result = administrativeDivisionService.searchCities(queryDTO);
            return R.success(result);
        } catch (Exception e) {
            log.error("查询城市失败", e);
            return R.fail("查询失败");
        }
    }
    
    @Override
    public Map<String, String> convertCodeToName(AdministrativeDivisionQueryDTO queryDTO) {
        return administrativeDivisionService.convertCodeToName(queryDTO);
    }
}