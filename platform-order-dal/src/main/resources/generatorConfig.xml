<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<!--suppress MybatisGenerateCustomPluginInspection -->
<generatorConfiguration>
    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
            <property name="lombok" value="Data"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="******************************************************************************************************************************"
                        userId="user-dev"
                        password="dasdwqce25bv3vr3">
        </jdbcConnection>

        <!--MyBatis 生成器只需要生成 Model-->
        <javaModelGenerator targetPackage="com.yuanshuo.platform.order.mapper.po"
                            targetProject="src/main/java"/>

        <sqlMapGenerator targetPackage="mapper"
                         targetProject="src/main/resources"/>

        <javaClientGenerator targetPackage="com.yuanshuo.platform.order.mapper"
                             targetProject="src/main/java"
                             type="XMLMAPPER"/>
        <table tableName="order_flow" domainObjectName="OrderFlow">
            <generatedKey column="id" sqlStatement="JDBC"/>
        </table>
<!--        <table tableName="order_item" domainObjectName="OrderItem">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="order_address" domainObjectName="OrderAddress">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="order_address" domainObjectName="OrderAddress">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
    </context>
</generatorConfiguration>