package com.yuanshuo.platform.order.rpc.impl;

import cn.hutool.core.bean.BeanUtil;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.feign.TradeOrderFeign;
import com.yuanshuo.platform.order.api.model.dto.AuditTradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.CloseTradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.OrderRefundDTO;
import com.yuanshuo.platform.order.api.model.dto.TradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.query.*;
import com.yuanshuo.platform.order.api.model.vo.OrderOperationLogVO;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderStatisticsVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.server.OrderRefundService;
import com.yuanshuo.platform.order.server.TradeOrderService;
import com.yuanshuo.platform.order.server.flow.TradeFlowService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
/**
 * 订单统一入口
 */
@RestController
@Slf4j
public class TradeOrderFeignImpl implements TradeOrderFeign {

    @Autowired
    TradeOrderService tradeOrderService;
    @Autowired
    OrderRefundService orderRefundService;
    @Autowired
    TradeFlowService tradeFlowService;

    @Override
    public R<TradeOrderVO> createOrder(@Valid TradeOrderDTO tradeOrderDTO) {
        log.info("createOrder - input: {}", tradeOrderDTO);
        TradeOrderVO order = tradeFlowService.createOrder(tradeOrderDTO);
        log.info("createOrder - output: {}", order);
        return  R.success(order);
    }

    @Override
    public R<Void> cancelOrderByUser(@Valid CloseTradeOrderDTO closeOrderDTO) {
        log.info("cancelOrder - input: {}", closeOrderDTO);
        tradeFlowService.cancelOrderByUser(closeOrderDTO);
        log.info("cancelOrder - output: void");
        return  R.success();
    }

    @Override
    public R<Void> cancelOrderByOperations(CloseTradeOrderDTO closeOrderDTO) {
        log.info("cancelOrder - input: {}", closeOrderDTO);
        tradeFlowService.cancelOrderByOperations(closeOrderDTO);
        log.info("cancelOrder - output: void");
        return  R.success();
    }

    @Override
    public R<TableDataInfo<TradeOrderVO>> queryPageOrder(@Valid TradeOrderQueryPageDTO tradeOrderQueryPageDTO) {
        log.info("queryPageOrder - input: {}", tradeOrderQueryPageDTO);
        TableDataInfo<TradeOrderVO> tradeOrderDOTableDataInfo = tradeOrderService.queryPageOrder(tradeOrderQueryPageDTO);
        TableDataInfo result = BeanUtil.copyProperties(tradeOrderDOTableDataInfo, TableDataInfo.class);
        return   R.success(result);
    }

    @Override
    public R<TradeOrderVO> queryOneOrder(@Valid TradeOrderQueryOneDTO tradeOrderQueryOneDTO) {
        log.info("queryOneOrder - input: {}", tradeOrderQueryOneDTO);
        TradeOrderVO data = tradeOrderService.queryOneOrder(tradeOrderQueryOneDTO);
        return   R.success(data);
    }

    @Override
    public R<List<TradeOrderVO>> queryListOrder(@Valid TradeOrderQueryListDTO tradeOrderQueryListDTO) {
        log.info("queryListOrder - input: {}", tradeOrderQueryListDTO);
        /**
         * 以后再说
         */
        log.info("queryListOrder - output: null");
        return null;
    }

    @Override
    public R<TradeOrderStatisticsVO> queryOrderStatistics(@Valid TradeOrderQueryStatisticDTO tradeOrderQueryStatisticDTO) {
        log.info("queryOrderStatistics - input: {}", tradeOrderQueryStatisticDTO);
        Map<String, String> resultMap = tradeOrderService.queryOrderStatistics(tradeOrderQueryStatisticDTO);
        TradeOrderStatisticsVO tradeOrderStatisticsVO = BeanUtil.toBean(resultMap, TradeOrderStatisticsVO.class);
        return R.success(tradeOrderStatisticsVO);
    }

    @Override
    public R<Void> auditOrder(@Valid AuditTradeOrderDTO auditOrderDTO) {
        log.info("auditOrder - input: {}", auditOrderDTO);
        tradeOrderService.auditOrder(auditOrderDTO);
        log.info("auditOrder - output: void");
        return  R.success();
    }

//    @Override
//    public R<Void> orderPayCallback(@Valid CallbackOrderAfterPaySuccessDTO callbackOrderAfterPaySuccessDTO) {
//        log.info("orderPayCallback - input: {}", callbackOrderAfterPaySuccessDTO);
//        tradeFlowService.callbackOrderAfterPaySuccess(callbackOrderAfterPaySuccessDTO);
//        log.info("orderPayCallback - output: void");
//        return  R.success();
//    }

    @Override
    public R<OrderRefundVO> createOrderRefund(@Valid OrderRefundDTO orderRefundDTO) {
        log.info("createOrderRefund - input: {}", orderRefundDTO);
        OrderRefundVO orderRefundVO = tradeFlowService.createOrderRefund(orderRefundDTO);
        return R.success(orderRefundVO);
    }

    @Override
    public String test() {
        return "";
    }

    @Override
    public R<List<OrderOperationLogVO>> queryListOperatorLog(TradeOrderLogQueryPageDTO tradeOrderLogQueryPageDTO) {
        log.info("queryListOperatorLog - input: {}", tradeOrderLogQueryPageDTO);
        List<OrderOperationLogVO> orderOperationLogVOS = tradeOrderService.queryListOperatorLog(tradeOrderLogQueryPageDTO);
        return R.success(orderOperationLogVOS);
    }

    @Override
    public R<TableDataInfo<OrderOperationLogVO>> queryPageOperatorLog(TradeOrderLogQueryPageDTO tradeOrderLogQueryPageDTO) {
        log.info("queryPageOperatorLog - input: {}", tradeOrderLogQueryPageDTO);
        TableDataInfo<OrderOperationLogVO> tableDataInfo = tradeOrderService.queryPageOperatorLog(tradeOrderLogQueryPageDTO);
        return R.success(tableDataInfo);
    }

}
