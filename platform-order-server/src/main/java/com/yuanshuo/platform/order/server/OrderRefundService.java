package com.yuanshuo.platform.order.server;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.yuanshuo.platform.order.api.enums.EnumOrderRefundStatus;
import com.yuanshuo.platform.order.api.model.dto.OrderRefundDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.common.BusinessException;
import com.yuanshuo.platform.order.entity.DataSnapshotEntity;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.flow.context.order.CreateOrderRefundContext;
import com.yuanshuo.platform.order.mapper.OrderRefundMapper;
import com.yuanshuo.platform.order.mapper.TradeOrderMapper;
import com.yuanshuo.platform.order.mapper.po.OrderRefund;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class OrderRefundService {
    @Autowired
    OrderRefundMapper orderRefundMapper;
    @Autowired
    TradeOrderService tradeOrderService;
    @Autowired
    TradeOrderMapper tradeOrderMapper;

    @Transactional(rollbackFor = Exception.class)
    public OrderRefundVO createOrderRefund(CreateOrderRefundContext createOrderRefundContext){
        OrderRefundDTO orderRefundDTO = createOrderRefundContext.getOrderRefundDTO();
        OrderRefund orderRefund = BeanUtil.copyProperties(orderRefundDTO, OrderRefund.class);

        DataSnapshotEntity beforeDataSnapshotEntity = tradeOrderService.buildDataSnapshotEntityBeforeUpdate(orderRefund.getTradeOrderNo());

        //插入退单表
        orderRefund.setTotalAmount(orderRefundDTO.getRefundAmount());
        orderRefund.setReceivedAmount(orderRefundDTO.getRefundAmount());
        orderRefund.setOrderRefundNo(RandomUtil.randomNumbers(16));
        orderRefund.setRefundStatus(EnumOrderRefundStatus.REFUND_INITIALIZE.getCode());
        orderRefundMapper.insertSelective(orderRefund);

        DataSnapshotEntity afterDataSnapshotEntity = tradeOrderService.buildAfterDataSnapshotEntityWithUpdateDate(orderRefund, beforeDataSnapshotEntity);
        tradeOrderService.insertOrderStatusHistoryAndLog(beforeDataSnapshotEntity,afterDataSnapshotEntity,createOrderRefundContext);

        OrderRefundVO orderRefundVO = BeanUtil.copyProperties(orderRefund, OrderRefundVO.class);
        return orderRefundVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRefundOrderStatusTo(BaseContext baseContext,EnumOrderRefundStatus status) {
        String orderRefundNo = baseContext.getOrderRefundNo();

        OrderRefund dataBaseOrderRefund = queryByOrderRefundNoMust(orderRefundNo);
        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setId(dataBaseOrderRefund.getId());
        orderRefund.setOrderRefundNo(baseContext.getOrderRefundNo());
        orderRefund.setRefundStatus(status.getCode());

        updateOrderByOrderRefund(orderRefund, baseContext);
    }





    private void updateOrderByOrderRefund(OrderRefund orderRefund,BaseContext baseContext) {

        DataSnapshotEntity beforeDataSnapshotEntity = tradeOrderService.buildDataSnapshotEntityBeforeUpdate(baseContext.getTradeOrderNo());

        orderRefundMapper.updateByPrimaryKeySelective(orderRefund);

        DataSnapshotEntity afterDataSnapshotEntity = tradeOrderService.buildAfterDataSnapshotEntityWithUpdateDate(orderRefund, beforeDataSnapshotEntity);
        tradeOrderService.insertOrderStatusHistoryAndLog(beforeDataSnapshotEntity,afterDataSnapshotEntity,baseContext);
    }



    public OrderRefund queryByOrderRefundNoMust(String orderRefundNo) {
        OrderRefund query = new OrderRefund();
        query.setOrderRefundNo(orderRefundNo);
        OrderRefund orderRefund = orderRefundMapper.selectOne(query);
        if(Objects.isNull(orderRefund)){
            throw new BusinessException("退款单不存在");
        }
        return orderRefund;
    }
    public List<OrderRefundVO> queryOrderRefund(String tradeOrderNo){
        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setTradeOrderNo(tradeOrderNo);
        List<OrderRefund> orderRefunds = orderRefundMapper.select(orderRefund);
        if(CollUtil.isNotEmpty(orderRefunds)){
            return BeanUtil.copyToList(orderRefunds, OrderRefundVO.class);
        }
        return new ArrayList<>();
    }



}
