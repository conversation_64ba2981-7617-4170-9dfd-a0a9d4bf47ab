package com.yuanshuo.platform.order.server;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.platform.order.common.BusinessException;
import com.yuanshuo.platform.order.api.model.dto.OrderReservationRuleDTO;
import com.yuanshuo.platform.order.api.model.dto.query.OrderReservationRuleQueryDTO;
import jakarta.validation.Valid;
import com.yuanshuo.platform.order.mapper.OrderReservationRuleMapper;
import com.yuanshuo.platform.order.mapper.po.OrderReservationRule;
import com.yuanshuo.platform.order.mapper.query.OrderReservationRuleQuery;
import com.yuanshuo.platform.order.api.model.vo.OrderReservationRuleVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.ArrayList;

/**
 * 预约下单规则配置Service实现类
 */
@Service
public class OrderReservationRuleService  {
    
    @Autowired
    private OrderReservationRuleMapper orderReservationRuleMapper;
    
    @Autowired
    private AdministrativeDivisionService administrativeDivisionService;
    
    public void createRule(@Valid OrderReservationRuleDTO ruleDTO) {
        // 业务校验（包含城市规则冲突检查）
        validateBusinessRule(ruleDTO, false);
        
        // 构建PO对象
        OrderReservationRule rule = new OrderReservationRule();
        rule.setRuleId(generateRuleId());
        rule.setCityCodes(String.join(",", ruleDTO.getCityCodes()));
        rule.setFutureDays(ruleDTO.getFutureDays());
        rule.setTimeInterval(ruleDTO.getTimeInterval());
        rule.setStatus(ruleDTO.getStatus());
        rule.setRemark(ruleDTO.getRemark());
        rule.setIsDelete(0);
        rule.setCreateUser(ruleDTO.getOperatorUserId().toString());
        rule.setCreateTime(new Date());
        rule.setUpdateTime(new Date());
        
        // 保存到数据库
        orderReservationRuleMapper.insertSelective(rule);
    }
    
    public void updateRule(@Valid OrderReservationRuleDTO ruleDTO) {
        // 业务校验（包含城市规则冲突检查）
        validateBusinessRule(ruleDTO, true);
        
        // 查询原规则
        OrderReservationRule existRule = orderReservationRuleMapper.selectByRuleId(ruleDTO.getRuleId());
        if (existRule == null) {
            throw new BusinessException("规则不存在");
        }
        
        // 更新规则信息
        existRule.setCityCodes(String.join(",", ruleDTO.getCityCodes()));
        existRule.setFutureDays(ruleDTO.getFutureDays());
        existRule.setTimeInterval(ruleDTO.getTimeInterval());
        existRule.setStatus(ruleDTO.getStatus());
        existRule.setRemark(ruleDTO.getRemark());
        existRule.setUpdateTime(new Date());
        
        // 更新到数据库
        orderReservationRuleMapper.updateByPrimaryKeySelective(existRule);
    }
    
    public void deleteRule(String ruleId, String operatorUserId, String operatorUserName) {
        if (!StringUtils.hasText(ruleId)) {
            throw new BusinessException("规则ID不能为空");
        }
        
        // 查询规则是否存在
        OrderReservationRule existRule = orderReservationRuleMapper.selectByRuleId(ruleId);
        if (existRule == null) {
            throw new BusinessException("规则不存在");
        }
        
        // 逻辑删除
        existRule.setIsDelete(1);
        existRule.setUpdateTime(new Date());
        
        orderReservationRuleMapper.updateByPrimaryKeySelective(existRule);
    }
    
    public TableDataInfo<OrderReservationRuleVO> queryRuleList(OrderReservationRuleQueryDTO queryDTO) {
        // 构建查询条件
        OrderReservationRuleQuery query = new OrderReservationRuleQuery();
        BeanUtils.copyProperties(queryDTO, query);
        query.setIsDelete(0); // 只查询未删除的记录
        PageHelper.startPage(queryDTO.getPageNo(), queryDTO.getPageSize());

        // 查询数据
        List<OrderReservationRule> rules = orderReservationRuleMapper.selectByQuery(query);
        
        // 转换为VO
        List<OrderReservationRuleVO> voList = new ArrayList<>();
        for (OrderReservationRule rule : rules) {
            OrderReservationRuleVO vo = convertToVO(rule);
            voList.add(vo);
        }
        PageInfo page = new PageInfo(rules);
        // 构建分页结果
        TableDataInfo<OrderReservationRuleVO> result = new TableDataInfo<>();
        result.setRows(voList);
        result.setTotal(page.getTotal());
        
        return result;
    }
    
    public OrderReservationRuleVO getRuleDetail(String ruleId) {
        if (!StringUtils.hasText(ruleId)) {
            throw new BusinessException("规则ID不能为空");
        }
        
        OrderReservationRule rule = orderReservationRuleMapper.selectByRuleId(ruleId);
        if (rule == null) {
            throw new BusinessException("规则不存在");
        }
        
        return convertToVO(rule);
    }
    
    public OrderReservationRuleVO getRuleByCityCode(String cityCode) {
        if (!StringUtils.hasText(cityCode)) {
            throw new BusinessException("城市编码不能为空");
        }
        
        OrderReservationRule rule = orderReservationRuleMapper.selectByCityCode(cityCode);
        if (rule == null) {
            return null;
        }
        
        return convertToVO(rule);
    }
    
    /**
     * 业务规则校验
     */
    private void validateBusinessRule(OrderReservationRuleDTO ruleDTO, boolean isUpdate) {
        // 更新时校验规则ID
        if (isUpdate && !StringUtils.hasText(ruleDTO.getRuleId())) {
            throw new BusinessException("规则ID不能为空");
        }
        
        // 检查城市规则冲突
        if (ruleDTO.getAllowCityTransfer() != null && ruleDTO.getAllowCityTransfer()) {
            // 允许城市转移，处理冲突城市
            handleCityTransfer(ruleDTO.getCityCodes(), isUpdate ? ruleDTO.getRuleId() : null);
        } else {
            // 不允许城市转移，直接检查冲突
            checkCityRuleConflict(ruleDTO.getCityCodes(), isUpdate ? ruleDTO.getRuleId() : null);
        }
    }
    
    /**
     * 检查城市规则冲突
     */
    private void checkCityRuleConflict(List<String> cityCodes, String excludeRuleId) {
        for (String cityCode : cityCodes) {
            OrderReservationRule existRule = orderReservationRuleMapper.selectByCityCode(cityCode);
            if (existRule != null && !existRule.getRuleId().equals(excludeRuleId)) {
                // 获取城市中文名
                String cityName = administrativeDivisionService.getNameByCode(cityCode);
                String displayName = cityName != null ? cityName : cityCode;
                throw new BusinessException(displayName + "市已经关联规则,规则id:" + existRule.getRuleId() + ",请确认使用哪个规则");
            }
        }
    }
    
    /**
     * 处理城市转移（当允许城市转移时，将冲突城市从老规则中删除）
     */
    private void handleCityTransfer(List<String> cityCodes, String excludeRuleId) {
        for (String cityCode : cityCodes) {
            OrderReservationRule existRule = orderReservationRuleMapper.selectByCityCode(cityCode);
            if (existRule != null && !existRule.getRuleId().equals(excludeRuleId)) {
                // 从老规则中删除该城市
                removeCityFromRule(existRule, cityCode);
            }
        }
    }
    
    /**
     * 从规则中删除指定城市
     */
    private void removeCityFromRule(OrderReservationRule rule, String cityCodeToRemove) {
        if (!StringUtils.hasText(rule.getCityCodes())) {
            return;
        }
        
        List<String> cityCodes = new ArrayList<>(Arrays.asList(rule.getCityCodes().split(",")));
        cityCodes.remove(cityCodeToRemove);
        
        if (cityCodes.isEmpty()) {
            // 如果城市列表为空，删除整个规则
            rule.setIsDelete(1);
            rule.setUpdateTime(new Date());
            orderReservationRuleMapper.updateByPrimaryKeySelective(rule);
        } else {
            // 更新城市列表
            rule.setCityCodes(String.join(",", cityCodes));
            rule.setUpdateTime(new Date());
            orderReservationRuleMapper.updateByPrimaryKeySelective(rule);
        }
    }
    
    /**
     * 生成规则ID
     */
    private String generateRuleId() {
        return "RULE_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    /**
     * 转换为VO对象
     */
    private OrderReservationRuleVO convertToVO(OrderReservationRule rule) {
        OrderReservationRuleVO vo = new OrderReservationRuleVO();
        BeanUtils.copyProperties(rule, vo);
        
        // 处理城市编码
        if (StringUtils.hasText(rule.getCityCodes())) {
            vo.setCityCodes(Arrays.asList(rule.getCityCodes().split(",")));
        }
        
        // 处理状态描述
        vo.setStatusDesc(rule.getStatus() == 1 ? "启用" : "禁用");
        
        return vo;
    }
}