<flow>
    <!--只包括跟取消有关的编排文件-->
    <!-- 更新状态为取消中并发送mq -->
    <chain name="updateOrderStatusToCancellingAndSendMq">
        THEN(
            nodeUpdateOrderStatusToCancelling,
            nodeSendMqForTradeVo
        )
    </chain>
    <!-- 用户（app）取消订单 -->
    <chain name="userCancelOrderForAppPlaceOrder">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"},{"status":"paying","process":"updateOrderStatusToCancellingAndSendMq"}]';
        mqData = '{"tag":"order_cancel"}';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            )
            .to(
                updateOrderStatusToCancellingAndSendMq.bind("mqData",mqData),
                nodeUpdateOrderStatusToCancelled
            ),
            syncStatus
        );
    </chain>

    <!-- 运营（app）取消订单 -->
    <chain name="operationsCancelOrderForAppPlaceOrder">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"},{"status":"paying","process":"updateOrderStatusToCancellingAndSendMq"}]';
        mqData = '{"tag":"order_cancel"}';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            )
            .to(
                updateOrderStatusToCancellingAndSendMq.bind("mqData",mqData),
                nodeUpdateOrderStatusToCancelled
            ),
            syncStatus
        );
    </chain>

    <!-- 系统超时（app）取消订单 -->
    <chain name="timeOutCancelOrderForAppPlaceOrder">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"},{"status":"paying","process":"updateOrderStatusToCancellingAndSendMq"}]';
        mqData = '{"tag":"order_cancel"}';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            )
            .to(
                updateOrderStatusToCancellingAndSendMq.bind("mqData",mqData),
                nodeUpdateOrderStatusToCancelled
            ),
            syncStatus
        );
    </chain>


    <!-- 用户（销售小工具）取消订单 -->
    <chain name="userCancelOrderForTradeLittleSeller">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"},{"status":"paying","process":"updateOrderStatusToCancellingAndSendMq"}]';
        mqData = '{"tag":"order_cancel"}';
        SWITCH(
            switchStatusToProcess.bind("switchStatus",cmpData)
        )
        .to(
            updateOrderStatusToCancellingAndSendMq.bind("mqData",mqData),
            nodeUpdateOrderStatusToCancelled
        );
    </chain>

    <!-- 运营（销售小工具）取消订单 -->
    <chain name="operationsCancelOrderForTradeLittleSeller">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"},{"status":"paying","process":"updateOrderStatusToCancellingAndSendMq"}]';
        mqData = '{"tag":"order_cancel"}';
        SWITCH(
            switchStatusToProcess.bind("switchStatus",cmpData)
        )
        .to(
            updateOrderStatusToCancellingAndSendMq.bind("mqData",mqData),
            nodeUpdateOrderStatusToCancelled
        );
    </chain>

    <!-- 系统超时（销售小工具）取消订单 -->
    <chain name="timeOutCancelOrderForTradeLittleSeller">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"},{"status":"paying","process":"updateOrderStatusToCancellingAndSendMq"}]';
        mqData = '{"tag":"order_cancel"}';
        SWITCH(
            switchStatusToProcess.bind("switchStatus",cmpData)
        )
        .to(
            updateOrderStatusToCancellingAndSendMq.bind("mqData",mqData),
            nodeUpdateOrderStatusToCancelled
        );
    </chain>


</flow>
