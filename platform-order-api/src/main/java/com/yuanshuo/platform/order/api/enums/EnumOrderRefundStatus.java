package com.yuanshuo.platform.order.api.enums;

public enum EnumOrderRefundStatus {
    REFUND_INITIALIZE("refund_initialize","退款初始化"),
    UN_REFUNDED("un_refunded", "未退款"),
    REFUNDED("refunded", "支付渠道返回成功结果，资金已退回用户"),
    REFUNDING("refunding", "退款指令已提交，等待支付渠道处理"),
    REFUND_FAILED("refund_failed", "退款失败");

    private String code;
    private String msg;

    EnumOrderRefundStatus(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
    //根据code获取枚举
    public static EnumOrderRefundStatus getByCode(String code) {
        for (EnumOrderRefundStatus status : EnumOrderRefundStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
