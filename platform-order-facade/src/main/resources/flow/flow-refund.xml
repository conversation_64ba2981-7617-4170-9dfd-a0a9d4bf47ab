<flow>

    <!-- App改价单退款成功 -->
    <chain name="callbackPriceChangeOrderRefundSuccess">
        THEN(
        nodeUpdatePriceChangeToRefunded
        );
    </chain>

    <!--只包括跟退款有关的编排文件-->
    <!-- 支付服务退款单创建回调 -->
    <chain name="callbackPayRefundCreateForAppPlaceOrder">
        cmpData = '[{"refundStatus":"refund_initialize","process":"nodeUpdateOrderRefundStatusToRefunding"}]';
        cmpDataRefundOrder = '[{"refundOrderStatus":"refund_initialize","process":"nodeUpdateRefundOrderStatusToRefunding"}]';
        THEN(
            SWITCH(
                switchRefundStatusToProcess.bind("switchRefundStatus",cmpData)
            )
            .to(
                nodeUpdateOrderRefundStatusToRefunding
            ),
            SWITCH(
                switchRefundOrderStatusToProcess.bind("switchRefundOrderStatus",cmpDataRefundOrder)
            )
            .to(
                nodeUpdateRefundOrderStatusToRefunding
            )

        );
    </chain>
    <!-- 支付服务退款成功 -->
    <chain name="callbackPayRefundSuccessForAppPlaceOrder">
        cmpData = '[{"status":"refunding","process":"nodeUpdateOrderStatusToClosed"}]';
        cmpDataRefund = '[{"refundStatus":"refund_initialize","process":"nodeWarnProcess"},{"refundStatus":"refunding","process":"nodeUpdateOrderRefundStatusToRefunded"}]';
        cmpDataRefundOrder = '[{"refundOrderStatus":"refund_initialize","process":"nodeWarnProcess"},{"refundOrderStatus":"refunding","process":"nodeUpdateRefundOrderStatusToRefunded"}]';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            ) .to(
                nodeUpdateOrderStatusToClosed
            ),
            SWITCH(
                switchRefundStatusToProcess.bind("switchRefundStatus",cmpDataRefund)
            ).to(
                nodeWarnProcess,
                nodeUpdateOrderRefundStatusToRefunded
            ),
            SWITCH(
                switchRefundOrderStatusToProcess.bind("switchRefundOrderStatus",cmpDataRefundOrder)
            )
            .to(
                nodeWarnProcess,
                nodeUpdateRefundOrderStatusToRefunded
            ),
            syncStatus
        );
    </chain>

    <!-- 支付服务退款失败 -->
    <chain name="callbackPayRefundFailForAppPlaceOrder">
        cmpData = '[{"refundStatus":"refund_initialize","process":"nodeUpdateOrderRefundStatusToRefundFailed"},{"refundStatus":"refunding","process":"nodeUpdateOrderRefundStatusToRefundFailed"}]';
        cmpDataRefundOrder = '[{"refundOrderStatus":"refund_initialize","process":"nodeUpdateRefundOrderStatusToRefundFailed"},{"refundOrderStatus":"refunding","process":"nodeUpdateRefundOrderStatusToRefundFailed"}]';
        THEN(
            orderStatusRollback,
            SWITCH(
                switchRefundStatusToProcess.bind("switchRefundStatus",cmpData)
            ).to(
                nodeUpdateOrderRefundStatusToRefundFailed
            ),
            SWITCH(
                switchRefundOrderStatusToProcess.bind("switchRefundOrderStatus",cmpDataRefundOrder)
            )
            .to(
                nodeUpdateRefundOrderStatusToRefundFailed
            ),
            syncStatus
        );
    </chain>



    <!-- 支付服务退款单创建 -->
    <chain name="callbackPayRefundCreateForTradeLittleSeller">
        cmpData = '[{"refundStatus":"refund_initialize","process":"nodeUpdateOrderRefundStatusToRefunding"}]';
        THEN(
            SWITCH(
                switchRefundStatusToProcess.bind("switchRefundStatus",cmpData)
            )
            .to(
                nodeUpdateOrderRefundStatusToRefunding
            ),
            nodeUpdateRefundOrderStatusToRefunding
        );
    </chain>

    <!-- 支付服务退款成功 -->
    <chain name="callbackPayRefundSuccessForTradeLittleSeller">
        cmpData = '[{"refundStatus":"refund_initialize","process":"nodeWarnProcess"},{"refundStatus":"refunding","process":"nodeEmptyProcess"}]';
        THEN(
             SWITCH(
                switchRefundStatusToProcess.bind("switchRefundStatus",cmpData)
            ).to(
                nodeWarnProcess,
                nodeEmptyProcess
            ),
            nodeUpdateOrderStatusToClosed,
            nodeUpdateOrderRefundStatusToRefunded,
            nodeUpdateRefundOrderStatusToRefunded
        );
    </chain>

    <!-- 支付服务退款失败 -->
    <chain name="callbackPayRefundFailForTradeLittleSeller">
        cmpData = '[{"refundStatus":"refund_initialize","process":"nodeWarnProcess"},{"refundStatus":"refunding","process":"nodeEmptyProcess"}]';
        THEN(
            SWITCH(
                switchRefundStatusToProcess.bind("switchRefundStatus",cmpData)
            ).to(
                nodeWarnProcess,
                nodeEmptyProcess
            ),
            nodeUpdateOrderStatusToCompleted,
            nodeUpdateOrderRefundStatusToRefundFailed,
            nodeUpdateRefundOrderStatusToRefundFailed
        );
    </chain>

</flow>
