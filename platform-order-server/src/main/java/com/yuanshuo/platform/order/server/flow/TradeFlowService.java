package com.yuanshuo.platform.order.server.flow;

import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yuanshuo.platform.order.api.model.dto.*;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.common.BusinessException;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.flow.context.PayPaidContext;
import com.yuanshuo.platform.order.flow.context.order.CreateOrderContext;
import com.yuanshuo.platform.order.flow.context.order.CreateOrderRefundContext;
import com.yuanshuo.platform.order.mapper.OrderFlowMapper;
import com.yuanshuo.platform.order.mapper.OrderFlowStepMapper;
import com.yuanshuo.platform.order.mapper.enums.FlowEventType;
import com.yuanshuo.platform.order.mapper.po.OrderFlow;
import com.yuanshuo.platform.order.mapper.po.OrderFlowStep;
import com.yuanshuo.platform.order.mapper.po.TradeOrder;
import com.yuanshuo.platform.order.server.TradeOrderService;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class TradeFlowService {
    @Resource
    private FlowExecutor flowExecutor;
    @Resource
    private OrderFlowMapper orderFlowMapper;
    @Resource
    private OrderFlowStepMapper orderFlowStepMapper;
    @Autowired
    private TradeOrderService tradeOrderService;

    @SneakyThrows
    @Transactional
    public TradeOrderVO createOrder(TradeOrderDTO tradeOrderDTO) {
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrderDTO.getOrderType(), FlowEventType.CREATE_ORDER);
        //创建订单无订单编号
//        createOrderContext.setTradeOrderNo();
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep( orderFlowStep,tradeOrderDTO);
        CreateOrderContext createOrderContext = new CreateOrderContext(baseContext);
        createOrderContext.setTradeOrderDTO(tradeOrderDTO);


        LiteflowResponse response = flowExecutor.execute2Resp(createOrderContext.getChainId(), null, createOrderContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
        TradeOrderVO tradeOrderVO = response.getContextBean(CreateOrderContext.class).getTradeOrderVO();
        return tradeOrderVO;
    }


    /**
     * 从OrderFlowStep构建BaseContext的链ID和链名称
     */
    private BaseContext buildBaseContextChainIdAndChainNameFromStep( OrderFlowStep orderFlowStep,BaseDTO baseDTO) {
        BaseContext baseContext = new BaseContext();
        baseContext.setChainId(orderFlowStep.getChainId());
        baseContext.setChainName(orderFlowStep.getChainName());
        baseContext.setOperatorUserId(baseDTO.getOperatorUserId());
        baseContext.setOperatorUserName(baseDTO.getOperatorUserName());
        return baseContext;
    }

    /**
     * 根据订单类型和事件类型查询流程步骤
     */
    private OrderFlowStep queryFlowStepByOrderTypeAndEventType(String orderType, FlowEventType flowEventType) {
        OrderFlowStep query = new OrderFlowStep();
        query.setOrderType(orderType);
        query.setFlowEventType(flowEventType);
        query.setIsDelete(0); // 确保查询未删除的有效流程
        OrderFlowStep orderFlowStep = orderFlowStepMapper.selectOne(query);
        if (orderFlowStep == null) {
            throw new BusinessException("订单类型[" + orderType + "]下未找到事件类型[" + flowEventType + "]对应的有效流程配置");
        }
        if (orderFlowStep.getChainId() == null || orderFlowStep.getChainId().trim().isEmpty()){
            throw new BusinessException("订单类型[" + orderType + "]下事件类型[" + flowEventType + "]对应的流程配置链ID为空");
        }
        return orderFlowStep;
    }

    @SneakyThrows
    @Transactional
    public void callbackOrderAfterPayCreate(CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(callbackOrderAfterPayCreateDTO.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PAY_CREATE);

        BaseContext baseContext =  buildBaseContextChainIdAndChainNameFromStep( orderFlowStep,callbackOrderAfterPayCreateDTO);
        baseContext.setTradeOrderNo(callbackOrderAfterPayCreateDTO.getTradeOrderNo());

        log.info("flowExecutor into:{}", baseContext.getChainId());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void cancelOrderByUser(CloseTradeOrderDTO closeOrderDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(closeOrderDTO.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.USER_CANCEL_ORDER);

        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep( orderFlowStep,closeOrderDTO);
        baseContext.setTradeOrderNo(closeOrderDTO.getTradeOrderNo());

        log.info("flowExecutor into:{}", baseContext.getChainId());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void cancelOrderByOperations(CloseTradeOrderDTO closeOrderDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(closeOrderDTO.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.OPERATIONS_CANCEL_ORDER);

        BaseContext baseContext =  buildBaseContextChainIdAndChainNameFromStep(orderFlowStep,closeOrderDTO);
        baseContext.setTradeOrderNo(closeOrderDTO.getTradeOrderNo());


        log.info("flowExecutor into:{}", baseContext.getChainId());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }

    }

    @SneakyThrows
    @Transactional
    public void cancelOrderForTimeOut(CloseTradeOrderDTO closeOrderDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(closeOrderDTO.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.TIME_OUT_CANCEL_ORDER);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, closeOrderDTO);
        baseContext.setTradeOrderNo(closeOrderDTO.getTradeOrderNo());

        log.info("flowExecutor into:{}", baseContext.getChainId());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void callbackOrderAfterPayClose(CallbackOrderAfterPayCancelDTO callbackOrderAfterPayCancelDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(callbackOrderAfterPayCancelDTO.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PAY_CLOSE);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackOrderAfterPayCancelDTO);
        baseContext.setTradeOrderNo(callbackOrderAfterPayCancelDTO.getTradeOrderNo());

        log.info("flowExecutor into:{}", baseContext.getChainId());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void callbackOrderAfterPaySuccess(CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(callbackAfterPaySuccessDTO.getBusinessNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PAY_SUCCESS);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackAfterPaySuccessDTO);
        PayPaidContext payPaidContext = new PayPaidContext(baseContext);
        payPaidContext.setTradeOrderNo(callbackAfterPaySuccessDTO.getBusinessNo());
        payPaidContext.setAmount(callbackAfterPaySuccessDTO.getAmount());
        payPaidContext.setPayChannel(callbackAfterPaySuccessDTO.getPayChannel());

        LiteflowResponse response = flowExecutor.execute2Resp(payPaidContext.getChainId(), null, payPaidContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void callbackOrderAfterPayFail(CallbackOrderAfterPayFailDTO callbackOrderAfterPayFailDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(callbackOrderAfterPayFailDTO.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PAY_FAIL);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackOrderAfterPayFailDTO);
        baseContext.setTradeOrderNo(callbackOrderAfterPayFailDTO.getTradeOrderNo());

        log.info("flowExecutor into:{}", baseContext.getChainId());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    private OrderFlow queryFlowByOrderType(String orderType) {
        OrderFlow query = new OrderFlow();
        query.setOrderType(orderType);
        OrderFlow orderFlow = orderFlowMapper.selectOne(query);
        if (orderFlow == null) {
            throw new BusinessException(orderType + "未找到对应流程");
        }
        return orderFlow;
    }

    @SneakyThrows
    @Transactional
    public OrderRefundVO createOrderRefund(OrderRefundDTO orderRefundDTO) {
        String tradeOrderNo = orderRefundDTO.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CREATE_REFUND_ORDER);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, orderRefundDTO);
        CreateOrderRefundContext createOrderRefundContext = new CreateOrderRefundContext(baseContext, orderRefundDTO);
        createOrderRefundContext.setOrderRefundDTO(orderRefundDTO);
        createOrderRefundContext.setTradeOrderNo(tradeOrderNo);

        LiteflowResponse response = flowExecutor.execute2Resp(createOrderRefundContext.getChainId(), null, createOrderRefundContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
        OrderRefundVO orderRefundVO = response.getContextBean(CreateOrderRefundContext.class).getOrderRefundVO();
        return orderRefundVO;
    }

    @SneakyThrows
    @Transactional
    public void callbackOrderRefundAfterPayCreate(CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO) {
        String tradeOrderNo = callbackOrderRefundAfterPayCreateDTO.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PAY_REFUND_CREATE);

        BaseContext orderRefundContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackOrderRefundAfterPayCreateDTO);
        orderRefundContext.setTradeOrderNo(tradeOrderNo);
        orderRefundContext.setOrderRefundNo(callbackOrderRefundAfterPayCreateDTO.getOrderRefundNo());


        LiteflowResponse response = flowExecutor.execute2Resp(orderRefundContext.getChainId(), null, orderRefundContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void callbackOrderRefundAfterPaySuccess(CallbackAfterRefundSuccessDTO callbackAfterRefundSuccessDTO) {
        String tradeOrderNo = callbackAfterRefundSuccessDTO.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PAY_REFUND_SUCCESS);

        BaseContext orderRefundContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackAfterRefundSuccessDTO);
        orderRefundContext.setTradeOrderNo(tradeOrderNo);
        orderRefundContext.setOrderRefundNo(callbackAfterRefundSuccessDTO.getOrderRefundNo());


        LiteflowResponse response = flowExecutor.execute2Resp(orderRefundContext.getChainId(), null, orderRefundContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }

    }

    @SneakyThrows
    @Transactional
    public void callbackOrderRefundAfterPayFail(CallbackOrderRefundAfterPayFailDTO callbackOrderRefundAfterPayFailDTO) {
        String tradeOrderNo = callbackOrderRefundAfterPayFailDTO.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PAY_REFUND_FAIL);

        BaseContext orderRefundContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackOrderRefundAfterPayFailDTO);
        orderRefundContext.setTradeOrderNo(tradeOrderNo);
        orderRefundContext.setOrderRefundNo(callbackOrderRefundAfterPayFailDTO.getOrderRefundNo());

        LiteflowResponse response = flowExecutor.execute2Resp(orderRefundContext.getChainId(), null, orderRefundContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }

    }

    @SneakyThrows
    @Transactional
    public void callbackOrderAfterFulfillmentCreate(FulfillmentCallbackDTO fulfillmentCallbackDTO) {
        String tradeOrderNo = fulfillmentCallbackDTO.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_FULFILLMENT_CREATE);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, fulfillmentCallbackDTO);
        baseContext.setTradeOrderNo(tradeOrderNo);

        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }
    @SneakyThrows
    @Transactional
    public void callbackOrderAfterFulfillmentTransport(FulfillmentCallbackDTO fulfillmentCallbackDTO) {
        String tradeOrderNo = fulfillmentCallbackDTO.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_FULFILLMENT_TRANSPORT);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, fulfillmentCallbackDTO);
        baseContext.setTradeOrderNo(tradeOrderNo);

        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void callbackOrderAfterFulfillmentComplete(FulfillmentCallbackDTO fulfillmentCallbackDTO) {
        String tradeOrderNo = fulfillmentCallbackDTO.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_FULFILLMENT_COMPLETE);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, fulfillmentCallbackDTO);
        baseContext.setTradeOrderNo(tradeOrderNo);

        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void test(BaseContext baseContext) {

        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, baseContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }
}
