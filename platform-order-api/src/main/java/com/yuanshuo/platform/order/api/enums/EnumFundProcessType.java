package com.yuanshuo.platform.order.api.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 资金处理类型枚举
 */
public enum EnumFundProcessType {
    /**
     * 退款
     */
    REFUND("refund", "退款"),
    /**
     * 补款
     */
    SUPPLEMENT("supplement", "补款"),
    /**
     * 无需处理
     */
    NONE("none", "无");

    private String code;
    private String desc;

    EnumFundProcessType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EnumFundProcessType getByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findFirst()
                .orElse(null);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}