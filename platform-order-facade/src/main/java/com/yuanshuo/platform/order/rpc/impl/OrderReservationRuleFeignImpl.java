package com.yuanshuo.platform.order.rpc.impl;

import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.feign.OrderReservationRuleFeign;
import com.yuanshuo.platform.order.common.BusinessException;
import com.yuanshuo.platform.order.dto.OrderReservationRuleDTO;
import com.yuanshuo.platform.order.api.model.dto.query.OrderReservationRuleQueryDTO;
import com.yuanshuo.platform.order.server.OrderReservationRuleService;
import com.yuanshuo.platform.order.api.model.vo.OrderReservationRuleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

/**
 * 预约下单规则配置Feign接口实现
 */
@RestController
public class OrderReservationRuleFeignImpl implements OrderReservationRuleFeign {
    
    @Autowired
    private OrderReservationRuleService orderReservationRuleService;
    
    @Override
    public R<Void> createRule(@Valid OrderReservationRuleDTO ruleDTO) {
        try {
            orderReservationRuleService.createRule(ruleDTO);
            return R.success();
        } catch (BusinessException e) {
            return R.fail(e.getMessage());
        }
    }
    
    @Override
    public R<Void> updateRule(@Valid OrderReservationRuleDTO ruleDTO) {
        try {
            orderReservationRuleService.updateRule(ruleDTO);
            return R.success();
        } catch (BusinessException e) {
            return R.fail(e.getMessage());
        }
    }
    
    @Override
    public R<Void> deleteRule(String ruleId, String operatorUserId, String operatorUserName) {
        try {
            orderReservationRuleService.deleteRule(ruleId, operatorUserId, operatorUserName);
            return R.success();
        } catch (BusinessException e) {
            return R.fail(e.getMessage());
        }
    }
    
    @Override
    public R<TableDataInfo<OrderReservationRuleVO>> queryRuleList(OrderReservationRuleQueryDTO queryDTO) {
        try {
            TableDataInfo<OrderReservationRuleVO> result = orderReservationRuleService.queryRuleList(queryDTO);
            return R.success(result);
        } catch (BusinessException e) {
            return R.fail(e.getMessage());
        }
    }
    
    @Override
    public R<OrderReservationRuleVO> getRuleDetail(String ruleId) {
        try {
            OrderReservationRuleVO result = orderReservationRuleService.getRuleDetail(ruleId);
            return R.success(result);
        } catch (BusinessException e) {
            return R.fail(e.getMessage());
        }
    }
    
    @Override
    public R<OrderReservationRuleVO> getRuleByCityCode(String cityCode) {
        try {
            OrderReservationRuleVO result = orderReservationRuleService.getRuleByCityCode(cityCode);
            return R.success(result);
        } catch (BusinessException e) {
            return R.fail(e.getMessage());
        }
    }
}