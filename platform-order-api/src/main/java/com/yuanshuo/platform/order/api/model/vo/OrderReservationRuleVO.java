package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 预约下单规则配置VO
 */
@Data
public class OrderReservationRuleVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 城市编码列表
     */
    private List<String> cityCodes;
    
    /**
     * 城市名称列表（用于展示）
     */
    private List<String> cityNames;
    
    /**
     * 可以预约未来几天
     */
    private Integer futureDays;
    
    /**
     * 时间间隔（分钟）
     */
    private Integer timeInterval;
    
    /**
     * 规则状态（0: 禁用, 1: 启用）
     */
    private Integer status;
    
    /**
     * 规则状态描述
     */
    private String statusDesc;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}