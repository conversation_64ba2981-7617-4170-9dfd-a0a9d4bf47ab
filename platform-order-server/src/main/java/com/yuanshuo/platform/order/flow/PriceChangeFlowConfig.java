package com.yuanshuo.platform.order.flow;

import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowFact;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;
import com.yuanshuo.platform.order.api.model.vo.OrderPriceChangeVO;
import com.yuanshuo.platform.order.flow.context.PayPaidContext;
import com.yuanshuo.platform.order.flow.context.PayRefundContext;
import com.yuanshuo.platform.order.flow.context.pricechange.CreatePriceChangeContext;
import com.yuanshuo.platform.order.server.OrderPriceChangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@LiteflowComponent
@Slf4j
public class PriceChangeFlowConfig {
    @Autowired
    OrderPriceChangeService orderPriceChangeService;

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "createPriceChange", nodeName = "创建改价单", nodeType = NodeTypeEnum.COMMON)
    public void createPriceChange(NodeComponent bindCmp, @LiteflowFact("createPriceChangeContext") CreatePriceChangeContext createPriceChangeContext) {
        createPriceChangeContext.setNodeId(bindCmp.getNodeId());
        createPriceChangeContext.setNodeName(bindCmp.getName());
        OrderPriceChangeVO orderPriceChangeVO = orderPriceChangeService.changeOrderPrice(createPriceChangeContext.getOrderPriceChangeDTO(), createPriceChangeContext);
        createPriceChangeContext.setTradeOrderNo(orderPriceChangeVO.getTradeOrderNo());
        createPriceChangeContext.setOrderPriceChangeVO(orderPriceChangeVO);

    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdatePriceChangeToPaid", nodeName = "更新支付单状态为已支付", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdatePriceChangeToPaid(NodeComponent bindCmp, @LiteflowFact("payPaidContext") PayPaidContext payPaidContext) {
        payPaidContext.setNodeId(bindCmp.getNodeId());
        payPaidContext.setNodeName(bindCmp.getName());
        orderPriceChangeService.updatePriceChangeToPaid(payPaidContext);
    }

    //有金额
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdatePriceChangeToRefunded", nodeName = "更新支付单状态为已退款", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdatePriceChangeToRefunded(NodeComponent bindCmp, @LiteflowFact("payRefundContext") PayRefundContext payRefundContext) {
        payRefundContext.setNodeId(bindCmp.getNodeId());
        payRefundContext.setNodeName(bindCmp.getName());
        orderPriceChangeService.updatePriceChangeToRefunded(payRefundContext);
    }

}
