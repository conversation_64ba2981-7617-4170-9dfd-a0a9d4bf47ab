<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderItemExtendsMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderItemExtends">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="order_item_no" jdbcType="VARCHAR" property="orderItemNo" />
    <result column="ext_data" jdbcType="VARCHAR" property="extData" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>