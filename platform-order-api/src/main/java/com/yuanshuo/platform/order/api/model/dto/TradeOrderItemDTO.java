package com.yuanshuo.platform.order.api.model.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;


/**
 * 商品订单详情
 */
@Data
public class TradeOrderItemDTO {

    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品code
     */
    private String itemCode;

    /**
     * 商品类型 目前只有 服务code: service
     */
    @NotEmpty
    private String itemType;

    /**
     * 单价,若没有可以不填
     */
    private Integer price;

    /**
     * 购买数量,若没有可以不填
     */
    private Integer quantity;


    /**
     * 商品扩展信息
     */
    private String itemExtData;

}
