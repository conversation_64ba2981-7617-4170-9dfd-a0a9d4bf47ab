-- 预约下单规则配置表
CREATE TABLE `order_reservation_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',
  `rule_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则ID',
  `city_codes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市编码，多个城市用逗号分隔',
  `future_days` int NOT NULL COMMENT '可以预约未来几天',
  `time_interval` int NOT NULL COMMENT '时间间隔（分钟）',
  `status` int DEFAULT '1' COMMENT '规则状态（0: 禁用, 1: 启用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `is_delete` int DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
  `create_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建记录的用户id,若没有则为当前服务名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_id` (`rule_id`),
  KEY `idx_city_codes` (`city_codes`(255)),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预约下单规则配置表';

-- 插入示例数据
INSERT INTO `order_reservation_rule` (`rule_id`, `city_codes`, `future_days`, `time_interval`, `status`, `remark`, `create_user`) VALUES
('RULE_DEFAULT_001', '110100,310100', 7, 30, 1, '北京、上海默认预约规则', 'system'),
('RULE_DEFAULT_002', '440100', 5, 60, 1, '广州预约规则', 'system'),
('RULE_DEFAULT_003', '440300', 3, 15, 1, '深圳预约规则', 'system');