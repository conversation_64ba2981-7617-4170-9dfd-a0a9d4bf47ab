package com.yuanshuo.platform.order.api.enums;

public enum EnumChannelType {
    WECHAT_MINI_PROGRAM("wechat_mini_program", "微信小程序"),
    ALIPAY_MINI_PROGRAM("alipay_mini_program", "支付宝小程序"),

    //微信公众号
    WECHAT_OFFICIAL_ACCOUNT("wechat_official_account", "微信公众号"),
    ALIPAY_OFFICIAL_ACCOUNT("alipay_official_account", "支付宝公众号"),

    ANDROID_APP("android_app", "安卓APP"),
    IOS_APP("ios_app", "苹果APP");

    private String code;
    private String msg;
    public String getCode()
    {
        return code;
    }
    public String getMsg()
    {
        return msg;
    }
    EnumChannelType(String code, String msg)
    {
        this.code = code;
        this.msg = msg;
    }
}
