package com.yuanshuo.platform.order.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.yuanshuo.common.entity.support.PageQuery;

import java.util.ArrayList;
import java.util.List;

public class PageUtil {

    public static void startPage(PageQuery pageQuery) {
        String orderBy = buildOrderBy(pageQuery);
        if (StrUtil.isNotEmpty(orderBy)) {
            PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize(), orderBy);
        } else {
            PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

        }
    }

    public static String buildOrderBy(PageQuery pageQuery) {
        List<String> orderList = new ArrayList<>();

        List<String> ascs = pageQuery.getAscs();
        if (CollUtil.isNotEmpty(ascs)) {
            for (String col : ascs) {
                if (col != null && !col.trim().isEmpty()) {
                    orderList.add(col + " ASC");
                }
            }
        }

        List<String> descs = pageQuery.getDescs();
        if (CollUtil.isNotEmpty(descs)) {
            for (String col : descs) {
                if (col != null && !col.trim().isEmpty()) {
                    orderList.add(col + " DESC");
                }
            }
        }

        if (orderList.isEmpty()) {
            return null; // 没有排序字段，返回空字符串
        }

        return String.join(", ", orderList);
    }
}
