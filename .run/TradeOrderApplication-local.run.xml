<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="TradeOrderApplication-local" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="local" />
    <module name="platform-order-facade" />
    <selectedOptions>
      <option name="environmentVariables" />
    </selectedOptions>
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.yuanshuo.platform.order.TradeOrderApplication" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.yuanshuo.platform.order.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>