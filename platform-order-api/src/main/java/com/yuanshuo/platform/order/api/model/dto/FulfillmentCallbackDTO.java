package com.yuanshuo.platform.order.api.model.dto;

import lombok.Data;

@Data
public class FulfillmentCallbackDTO extends BaseDTO{

    /**
     * 交易单号
     */
    private String tradeOrderNo;
    /**
     * 运输单号
     */
    private String transportNo;

    /**
     * 状态：dispatch_pending=待派车 dispatched=已派车 dispatch_fail=派车失败 pickup_pending=待取货
     * pickup_processing=取货中 transiting=运输中 unloading=卸货中 success=运输完成 fail=运输失败
     */
    private String status;
}
