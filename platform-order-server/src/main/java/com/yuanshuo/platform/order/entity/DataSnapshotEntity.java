package com.yuanshuo.platform.order.entity;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuanshuo.platform.order.mapper.po.*;
import lombok.Data;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;


@Data
public class DataSnapshotEntity  {

    private TradeOrder tradeOrder;

    private OrderAddress orderAddress;

    private OrderExtends orderExtends;

    private List<OrderItem> orderItemList;

    private List<OrderItemExtends> orderItemExtendsList;

    private List<OrderRefund> orderRefundList;

}
