<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <property name="LOG_HOME" value="${log.home:-/logs}"/>
    <property name="LOG_APP_NAME" value="${APP_NAME}"/>
    <property name="MAX_SINGLE_LOG_FILE_SIZE" value="${log.max-single-log-file-size:-100MB}"/>
    <property name="MAX_LOG_FILE_HISTORY" value="${log.max-log-file-history:-7}"/>
    <property name="LOG_FILE_TOTAL_CAPACITY" value="${log.log-file-total-capacity:-1GB}"/>
    <property name="ASYNC_DISCARDING_THRESHOLD" value="${log.async.discarding-threshold:-0}"/>
    <property name="ASYNC_LOG_QUEUE_SIZE" value="${log.async.queue-size:-256}"/>

    <!-- 添加 SkyWalking trace id 到日志格式中 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%tid] [%thread] %-5level %logger{36} - %msg%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${LOG_PATTERN}</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="ASYNC_STDOUT" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <queueSize>${ASYNC_LOG_QUEUE_SIZE}</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="STDOUT"/>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${LOG_APP_NAME}/${LOG_APP_NAME}.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${LOG_APP_NAME}/${LOG_APP_NAME}.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxFileSize>${MAX_SINGLE_LOG_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_LOG_FILE_HISTORY}</maxHistory>
            <totalSizeCap>${LOG_FILE_TOTAL_CAPACITY}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <queueSize>${ASYNC_LOG_QUEUE_SIZE}</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="FILE"/>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${LOG_HOME}/${LOG_APP_NAME}/${LOG_APP_NAME}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${LOG_APP_NAME}/${LOG_APP_NAME}.%d{yyyy-MM-dd}-error.%i.log.zip</fileNamePattern>
            <maxFileSize>${MAX_SINGLE_LOG_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_LOG_FILE_HISTORY}</maxHistory>
            <totalSizeCap>${LOG_FILE_TOTAL_CAPACITY}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <queueSize>${ASYNC_LOG_QUEUE_SIZE}</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="ASYNC_STDOUT"/>
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </root>
</configuration>