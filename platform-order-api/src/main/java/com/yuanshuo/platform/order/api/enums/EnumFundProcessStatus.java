package com.yuanshuo.platform.order.api.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 资金处理状态枚举
 */
public enum EnumFundProcessStatus {
    /**
     * 待处理
     */
    PENDING("pending", "待处理"),
    /**
     * 处理中
     */
    PROCESSING("processing", "处理中"),
    /**
     * 成功
     */
    SUCCESS("success", "成功"),
    /**
     * 失败
     */
    FAILED("failed", "失败"),
    /**
     * 无需处理
     */
    NOT_REQUIRED("not_required", "无需处理");

    private String code;
    private String desc;

    EnumFundProcessStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EnumFundProcessStatus getByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findFirst()
                .orElse(null);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}