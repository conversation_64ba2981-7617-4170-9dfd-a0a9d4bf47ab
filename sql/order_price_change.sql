
-- 2. 创建订单改价记录表
CREATE TABLE order_price_change (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID（自增）',
    trade_order_no VARCHAR(32) NOT NULL COMMENT '订单号',
    price_change_no VARCHAR(32) NOT NULL COMMENT '改价单号',
    change_type VARCHAR(20) NOT NULL COMMENT '改价类型：before_payment-支付前改价，after_payment-支付后改价',
    payment_status VARCHAR(20) NOT NULL COMMENT '改价时支付状态',
    
    -- 改价前金额信息（仅记录订单主表金额，不涉及明细）
    before_total_amount BIGINT NOT NULL COMMENT '改价前总金额（单位：分）',
    before_total_discount BIGINT DEFAULT 0 COMMENT '改价前总优惠金额（单位：分）',
    before_total_paid BIGINT DEFAULT 0 COMMENT '改价前总实付金额（单位：分）',
    
    -- 改价后金额信息
    after_total_amount BIGINT NOT NULL COMMENT '改价后总金额（单位：分）',
    after_total_discount BIGINT DEFAULT 0 COMMENT '改价后总优惠金额（单位：分）', 
    after_total_paid BIGINT NOT NULL COMMENT '改价后总实付金额（单位：分）',
    
    -- 差额信息
    amount_difference BIGINT NOT NULL COMMENT '金额差额（正数为涨价，负数为降价）（单位：分）',
    
    change_reason VARCHAR(500) COMMENT '改价原因',
    change_status VARCHAR(20) DEFAULT 'approved' COMMENT '改价状态：approved-已通过（自动审核）',
    
    -- 预留审核字段
    approval_status VARCHAR(20) DEFAULT 'auto_approved' COMMENT '审核状态：auto_approved-自动通过，manual_approved-人工通过，rejected-拒绝',
    approver_id BIGINT COMMENT '审核人ID',
    approver_name VARCHAR(100) COMMENT '审核人姓名',
    approval_time DATETIME COMMENT '审核时间',
    approval_remark VARCHAR(500) COMMENT '审核备注',
    
    is_delete INT DEFAULT 0 COMMENT '是否删除（0: 未删除, 1: 已删除）',
    create_user VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建记录的用户id,若没有则为当前服务名',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_trade_order_no (trade_order_no),
    INDEX idx_create_time (create_time),
    INDEX idx_change_type (change_type),
    INDEX idx_approval_status (approval_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单改价记录表';


ALTER TABLE order_price_change 
ADD COLUMN fund_process_status VARCHAR(20) DEFAULT 'pending' COMMENT '资金处理状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
ADD COLUMN fund_process_type VARCHAR(20) DEFAULT 'none' COMMENT '资金处理类型：none-无需处理，refund-退款，supplement-补款',
ADD COLUMN fund_process_amount BIGINT COMMENT '资金处理金额（单位：分）',
ADD COLUMN fund_process_no VARCHAR(32) COMMENT '资金处理单号（退款单号或补款单号）',
ADD COLUMN fund_process_time DATETIME COMMENT '资金处理完成时间',
ADD COLUMN fund_process_remark VARCHAR(500) COMMENT '资金处理备注',
ADD INDEX idx_fund_process_status (fund_process_status),
ADD INDEX idx_fund_process_type (fund_process_type);
-- 3. 为改价状态字段添加索引（如果需要经常按改价状态查询）
ALTER TABLE trade_order ADD INDEX idx_price_change_status (price_change_status);

-- 4. 插入测试数据（可选）
-- INSERT INTO order_price_change (
--     trade_order_no, change_type, payment_status,
--     before_total_amount, before_total_discount, before_total_paid,
--     after_total_amount, after_total_discount, after_total_paid,
--     amount_difference, change_reason, change_status, approval_status,
--     create_user
-- ) VALUES (
--     'TEST001', 'before_payment', 'unpaid',
--     100.00, 10.00, 90.00,
--     120.00, 10.00, 110.00,
--     20.00, '商品价格调整', 'approved', 'auto_approved',
--     '系统管理员'
-- );