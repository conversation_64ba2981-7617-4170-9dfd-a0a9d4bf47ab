package com.yuanshuo.platform.order.flow.context;

import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseContext {

    /**
     * liteflow 上下文传递值时，所有的地方都要用的值
     */
    /**
     * 订单编号
     */
    private String tradeOrderNo;
    /**
     * 退款单编号
     *
     */
    private String orderRefundNo;
    /**
     * 改价单单号
     */
    private String priceChangeNo;
    /**
     * EnumOperatorType 操作人类型
     */

    private Long operatorUserId;
    /**
     * EnumOperatorType 操作人类型
     */
    private String operatorUserName;

    /**
     * 操作节点ID
     */
    private String nodeId;

    /**
     * 操作节点名称
     */
    private String nodeName;

    /**
     * 操作流程
     */
    private String chainId;

    /**
     * 操作流程名称
     */
    private String chainName;

    /**
     * 扩展数据
     */
    private JSONObject jsonObject;


}
