package com.yuanshuo.platform.order;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.enums.*;
import com.yuanshuo.platform.order.api.model.dto.*;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.mapper.TradeOrderMapper;
import com.yuanshuo.platform.order.rpc.impl.TradeOrderFeignImpl;
import com.yuanshuo.platform.order.schedule.XxlSchedule;
import com.yuanshuo.platform.order.server.TradeOrderService;
import com.yuanshuo.platform.order.server.flow.TradeFlowService;
import com.yuanshuo.platform.order.util.MqUtil;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest(properties = {
        "spring.profiles.active=local"
})
public class AppCreateTests {

    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    private TradeOrderMapper tradeOrderMapper;
    @Autowired
    private TradeOrderFeignImpl traceOrderFeign;
    @Autowired
    TradeFlowService tradeFlowService;
    @Autowired
    TradeOrderService tradeOrderService;

    @Autowired
    XxlSchedule xxlSchedule;

    @Autowired
    MqUtil mqUtil;
    @Test
    public void createOrderForAppPlaceOrder()
    {
        // 订单主体信息
        TradeOrderVO order = getTradeOrderVO();
    }

    @Test
    public void callbackPayCreateForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();

        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

    }


    @Test
    public void userCancelOrderForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();


        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CloseTradeOrderDTO closeOrderDTO = new CloseTradeOrderDTO();
        closeOrderDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        closeOrderDTO.setNeedAudit(true);
        closeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        closeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.cancelOrderByUser(closeOrderDTO);
    }

    @Test
    public void operationsCancelOrderForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();


        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CloseTradeOrderDTO closeOrderDTO = new CloseTradeOrderDTO();
        closeOrderDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        closeOrderDTO.setNeedAudit(true);
        closeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        closeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.cancelOrderByOperations(closeOrderDTO);
    }
    @Test
    public void timeOutCancelOrderForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();


        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CloseTradeOrderDTO closeOrderDTO = new CloseTradeOrderDTO();
        closeOrderDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        closeOrderDTO.setNeedAudit(true);
        closeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        closeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.cancelOrderForTimeOut(closeOrderDTO);
    }




    @Test
    public void callbackPaySuccessForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderVOR.getTradeOrderNo());
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        callbackAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);
    }

    @Test
    public void callbackPayFailForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackOrderAfterPayFailDTO callbackOrderAfterPayFailDTO = new CallbackOrderAfterPayFailDTO();
        callbackOrderAfterPayFailDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayFailDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayFailDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayFail(callbackOrderAfterPayFailDTO);
    }

    @Test
    public void callbackPayCloseForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();

        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CloseTradeOrderDTO closeOrderDTO = new CloseTradeOrderDTO();
        closeOrderDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        closeOrderDTO.setNeedAudit(true);
        closeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        closeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.cancelOrderByUser(closeOrderDTO);

        CallbackOrderAfterPayCancelDTO callbackOrderAfterPayCancelDTO = new CallbackOrderAfterPayCancelDTO();
        callbackOrderAfterPayCancelDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCancelDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCancelDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayClose(callbackOrderAfterPayCancelDTO);
    }


    @Test
    public void callbackFulfillmentCreateForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderVOR.getTradeOrderNo());
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        callbackAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);

        FulfillmentCallbackDTO fulfillmentCallbackDTO = new FulfillmentCallbackDTO();
        fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
        fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());
        fulfillmentCallbackDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        fulfillmentCallbackDTO.setStatus("dispatch_pending");
        fulfillmentCallbackDTO.setTransportNo("123456789");
        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);

    }
    @Test
    public void callbackFulfillmentTransportForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderVOR.getTradeOrderNo());
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        callbackAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);


        FulfillmentCallbackDTO fulfillmentCallbackDTO = new FulfillmentCallbackDTO();
        fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
        fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());
        fulfillmentCallbackDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        fulfillmentCallbackDTO.setStatus("dispatch_pending");
        fulfillmentCallbackDTO.setTransportNo("123456789");

        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("transiting");
        tradeFlowService.callbackOrderAfterFulfillmentTransport(fulfillmentCallbackDTO);

    }

    @Test
    public void callbackFulfillmentCompleteForAppPlaceOrder() {

//        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
//        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
//        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
//        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
//        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
//        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);
//
//        CallbackOrderAfterPaySuccessDTO callbackOrderAfterPaySuccessDTO = new CallbackOrderAfterPaySuccessDTO();
//        callbackOrderAfterPaySuccessDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
//        callbackOrderAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
//        callbackOrderAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
//        callbackOrderAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
//        tradeFlowService.callbackOrderAfterPaySuccess(callbackOrderAfterPaySuccessDTO);


        FulfillmentCallbackDTO fulfillmentCallbackDTO = new FulfillmentCallbackDTO();
        fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
        fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());
//        fulfillmentCallbackDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        fulfillmentCallbackDTO.setTradeOrderNo("2025060417303870660");
        fulfillmentCallbackDTO.setStatus("dispatch_pending");
        fulfillmentCallbackDTO.setTransportNo("123456789");

//        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);
//
//        fulfillmentCallbackDTO.setStatus("transiting");
//        tradeFlowService.callbackOrderAfterFulfillmentTransport(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("completed");
        tradeFlowService.callbackOrderAfterFulfillmentComplete(fulfillmentCallbackDTO);
    }


    @Test
    public void createRefundOrderForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderVOR.getTradeOrderNo());
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        callbackAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);


        FulfillmentCallbackDTO fulfillmentCallbackDTO = new FulfillmentCallbackDTO();
        fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
        fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());
        fulfillmentCallbackDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        fulfillmentCallbackDTO.setStatus("dispatch_pending");
        fulfillmentCallbackDTO.setTransportNo("123456789");

        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("transiting");
        tradeFlowService.callbackOrderAfterFulfillmentTransport(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("completed");
        tradeFlowService.callbackOrderAfterFulfillmentComplete(fulfillmentCallbackDTO);


        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        orderRefundDTO.setRefundAmount(200);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        OrderRefundVO orderRefund = tradeFlowService.createOrderRefund(orderRefundDTO);
        System.out.println(orderRefund);

    }


    @Test
    public void callbackPayRefundCreateForAppPlaceOrder() {


        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderVOR.getTradeOrderNo());
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        callbackAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);


        FulfillmentCallbackDTO fulfillmentCallbackDTO = new FulfillmentCallbackDTO();
        fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
        fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());
        fulfillmentCallbackDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        fulfillmentCallbackDTO.setStatus("dispatch_pending");
        fulfillmentCallbackDTO.setTransportNo("123456789");

        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("transiting");
        tradeFlowService.callbackOrderAfterFulfillmentTransport(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("completed");
        tradeFlowService.callbackOrderAfterFulfillmentComplete(fulfillmentCallbackDTO);


        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        orderRefundDTO.setRefundAmount(200);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);
        OrderRefundVO orderRefundVO = orderRefund.getData();

        CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO = new CallbackOrderRefundAfterPayCreateDTO();
        callbackOrderRefundAfterPayCreateDTO.setTradeOrderNo(orderRefundVO.getTradeOrderNo());
        callbackOrderRefundAfterPayCreateDTO.setOrderRefundNo(orderRefundVO.getOrderRefundNo());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderRefundAfterPayCreate(callbackOrderRefundAfterPayCreateDTO);


    }


    @Test
    public void callbackPayRefundSuccessForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderVOR.getTradeOrderNo());
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        callbackAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);


        FulfillmentCallbackDTO fulfillmentCallbackDTO = new FulfillmentCallbackDTO();
        fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
        fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());
        fulfillmentCallbackDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        fulfillmentCallbackDTO.setStatus("dispatch_pending");
        fulfillmentCallbackDTO.setTransportNo("123456789");

        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("transiting");
        tradeFlowService.callbackOrderAfterFulfillmentTransport(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("completed");
        tradeFlowService.callbackOrderAfterFulfillmentComplete(fulfillmentCallbackDTO);


        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        orderRefundDTO.setRefundAmount(200);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);
        OrderRefundVO orderRefundVO = orderRefund.getData();

        CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO = new CallbackOrderRefundAfterPayCreateDTO();
        callbackOrderRefundAfterPayCreateDTO.setTradeOrderNo(orderRefundVO.getTradeOrderNo());
        callbackOrderRefundAfterPayCreateDTO.setOrderRefundNo(orderRefundVO.getOrderRefundNo());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderRefundAfterPayCreate(callbackOrderRefundAfterPayCreateDTO);


        CallbackAfterRefundSuccessDTO callbackAfterRefundSuccessDTO = new CallbackAfterRefundSuccessDTO();
        callbackAfterRefundSuccessDTO.setTradeOrderNo(orderRefundVO.getTradeOrderNo());
        callbackAfterRefundSuccessDTO.setOrderRefundNo(orderRefundVO.getOrderRefundNo());
        callbackAfterRefundSuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackAfterRefundSuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderRefundAfterPaySuccess(callbackAfterRefundSuccessDTO);


    }

    @Test
    public void callbackPayRefundFailForAppPlaceOrder() {

        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();
        CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderVOR.getTradeOrderNo());
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        callbackAfterPaySuccessDTO.setAmount(tradeOrderVOR.getTotalAmount());
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);


        FulfillmentCallbackDTO fulfillmentCallbackDTO = new FulfillmentCallbackDTO();
        fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
        fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());
        fulfillmentCallbackDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        fulfillmentCallbackDTO.setStatus("dispatch_pending");
        fulfillmentCallbackDTO.setTransportNo("123456789");

        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);

        fulfillmentCallbackDTO.setStatus("transiting");
        tradeFlowService.callbackOrderAfterFulfillmentTransport(fulfillmentCallbackDTO);

//        fulfillmentCallbackDTO.setStatus("completed");
//        tradeFlowService.callbackOrderAfterFulfillmentComplete(fulfillmentCallbackDTO);


        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        orderRefundDTO.setRefundAmount(200);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);
        OrderRefundVO orderRefundVO = orderRefund.getData();

        CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO = new CallbackOrderRefundAfterPayCreateDTO();
        callbackOrderRefundAfterPayCreateDTO.setTradeOrderNo(orderRefundVO.getTradeOrderNo());
        callbackOrderRefundAfterPayCreateDTO.setOrderRefundNo(orderRefundVO.getOrderRefundNo());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderRefundAfterPayCreate(callbackOrderRefundAfterPayCreateDTO);

        CallbackOrderRefundAfterPayFailDTO callbackOrderRefundAfterPayFailDTO = new CallbackOrderRefundAfterPayFailDTO();
        callbackOrderRefundAfterPayFailDTO.setTradeOrderNo(orderRefundVO.getTradeOrderNo());
        callbackOrderRefundAfterPayFailDTO.setOrderRefundNo(orderRefundVO.getOrderRefundNo());
        callbackOrderRefundAfterPayFailDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderRefundAfterPayFailDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());

        tradeFlowService.callbackOrderRefundAfterPayFail(callbackOrderRefundAfterPayFailDTO);

    }

    private TradeOrderVO getTradeOrderVO() {
        TradeOrderDTO tradeOrderDTO = new TradeOrderDTO();
        tradeOrderDTO.setOrderType(EnumOrderType.APP_PLACE_ORDER.getCode());
        tradeOrderDTO.setBizType(EnumBizType.TRANSPORT.getCode());
        tradeOrderDTO.setChannelType(EnumChannelType.ANDROID_APP.getCode());
        tradeOrderDTO.setSubBizType(EnumSubBizType.DRIVERLESS_CAR.getCode());
        tradeOrderDTO.setUserId(123L);
        tradeOrderDTO.setNickName("sss");
        tradeOrderDTO.setUserPhone("12345678941");
        tradeOrderDTO.setTotalAmount(200);
        tradeOrderDTO.setOrderExpireTime(DateUtil.offset(new Date(), DateField.MINUTE, 15));
        tradeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        tradeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeOrderDTO.setReserveTime(DateUtil.offset(new Date(), DateField.MINUTE, 60));

//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("goodsCategory", "人民币");
//        jsonObject.put("goodsUnit", "元");
//        jsonObject.put("goodsPic", "[\"1ac772701b9844ceb3da6c6396e8787d\",\"07db3b9acee8452c872a7f09d95f6b15\"]");
//        tradeOrderDTO.setOrderExtData(jsonObject.toString());
        //主订单地址 装货点至卸货点
        OrderAddressDTO orderAddress = MiniSellerTradeFeignTests.createTestOrderAddressDTO();
        tradeOrderDTO.setOrderAddress(orderAddress);

        List<TradeOrderDTO> childOrders = new ArrayList<>();
        //途径点信息  每一个途经点是一个子订单
//        TradeOrderDTO childTradeOrderOneDTO = new TradeOrderDTO();

//        childTradeOrderOneDTO.setOrderType(EnumOrderType.APP_PLACE_ORDER.getCode());
//        childTradeOrderOneDTO.setBizType(EnumBizType.TRANSPORT.getCode());
//        childTradeOrderOneDTO.setChannelType(EnumChannelType.ANDROID_APP.getCode());
//        childTradeOrderOneDTO.setSubBizType(EnumSubBizType.DRIVERLESS_CAR.getCode());
//        childTradeOrderOneDTO.setUserId(158222L);
//        childTradeOrderOneDTO.setNickName("sss");
//        childTradeOrderOneDTO.setUserPhone("12345678941");
//        childTradeOrderOneDTO.setOrderExpireTime(DateUtil.offset(new Date(), DateField.MINUTE, 15));
//        childTradeOrderOneDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
//        childTradeOrderOneDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
//
//        //装货点信息  第一为装货点 中间为途经点
//        OrderAddressDTO childOrderAddressDTOOne = new OrderAddressDTO();
//        childOrderAddressDTOOne.setSendAssociationCode(orderAddress.getSendAssociationCode());
//        childOrderAddressDTOOne.setSendPhone(orderAddress.getSendPhone());
//        childOrderAddressDTOOne.setSendName(orderAddress.getSendName());
//        childOrderAddressDTOOne.setSendCity(orderAddress.getSendCity());
//        childOrderAddressDTOOne.setSendCityCode(orderAddress.getSendCityCode());
//        childOrderAddressDTOOne.setSendProvince(orderAddress.getSendProvince());
//        childOrderAddressDTOOne.setSendProvinceCode(orderAddress.getSendProvinceCode());
//        childOrderAddressDTOOne.setSendAddressAlias(orderAddress.getSendAddressAlias());
//        childOrderAddressDTOOne.setSendStreet(orderAddress.getSendStreet());
//        childOrderAddressDTOOne.setSendDistrict(orderAddress.getSendDistrict());
//        childOrderAddressDTOOne.setSendDistrictCode(orderAddress.getSendDistrictCode());
//        childOrderAddressDTOOne.setSendTownship(orderAddress.getSendTownship());
//        childOrderAddressDTOOne.setSendTownshipCode(orderAddress.getSendTownshipCode());
//        childOrderAddressDTOOne.setSendLongitude(orderAddress.getSendLongitude());
//        childOrderAddressDTOOne.setSendLatitude(orderAddress.getSendLatitude());


//
//        //卸货点信息  最后一个为卸货点 中间为途经点
//        childOrderAddressDTOOne.setReceivedAssociationCode("66666");
//        childOrderAddressDTOOne.setReceivedPhone("18945673214");
//        childOrderAddressDTOOne.setReceivedName("王五");
//        childOrderAddressDTOOne.setReceivedProvince("江苏省");
//        childOrderAddressDTOOne.setReceivedProvinceCode("210101");
//
//        childOrderAddressDTOOne.setReceivedCity("南京市");
//        childOrderAddressDTOOne.setReceivedCityCode("210110");
//
//        childOrderAddressDTOOne.setReceivedDistrict("秦淮区");
//        childOrderAddressDTOOne.setReceivedDistrictCode("110108");
//
//        childOrderAddressDTOOne.setReceivedTownship("中华路街道");
//        childOrderAddressDTOOne.setReceivedTownshipCode("110108001");
//
//        childOrderAddressDTOOne.setReceivedAddressAlias("南南南南");
//        childOrderAddressDTOOne.setReceivedStreet("中华路街道1号");
//
//
//        childOrderAddressDTOOne.setReceivedLongitude(116.3265);
//        childOrderAddressDTOOne.setReceivedLatitude(39.9991);
//
//        childTradeOrderOneDTO.setOrderAddress(childOrderAddressDTOOne);
//        childOrders.add(childTradeOrderOneDTO);

//        TradeOrderDTO childTradeOrderTwoDTO = new TradeOrderDTO();

//        childTradeOrderTwoDTO.setOrderType(EnumOrderType.APP_PLACE_ORDER.getCode());
//        childTradeOrderTwoDTO.setBizType(EnumBizType.TRANSPORT.getCode());
//        childTradeOrderTwoDTO.setChannelType(EnumChannelType.ANDROID_APP.getCode());
//        childTradeOrderTwoDTO.setSubBizType(EnumSubBizType.DRIVERLESS_CAR.getCode());
//        childTradeOrderTwoDTO.setUserId(158222L);
//        childTradeOrderTwoDTO.setNickName("sss");
//        childTradeOrderTwoDTO.setUserPhone("12345678941");
//        childTradeOrderTwoDTO.setOrderExpireTime(DateUtil.offset(new Date(), DateField.MINUTE, 15));
//        childTradeOrderTwoDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
//        childTradeOrderTwoDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
//
//
//        OrderAddressDTO childOrderAddressDTOTwo = new OrderAddressDTO();
//        childOrderAddressDTOTwo.setSendAssociationCode(childOrderAddressDTOOne.getReceivedAssociationCode());
//        childOrderAddressDTOTwo.setSendPhone(childOrderAddressDTOOne.getReceivedPhone());
//        childOrderAddressDTOTwo.setSendName(childOrderAddressDTOOne.getReceivedName());
//        childOrderAddressDTOTwo.setSendCity(childOrderAddressDTOOne.getReceivedCity());
//        childOrderAddressDTOTwo.setSendCityCode(childOrderAddressDTOOne.getReceivedCityCode());
//        childOrderAddressDTOTwo.setSendProvince(childOrderAddressDTOOne.getReceivedProvince());
//        childOrderAddressDTOTwo.setSendProvinceCode(childOrderAddressDTOOne.getReceivedProvinceCode());
//        childOrderAddressDTOTwo.setSendAddressAlias(childOrderAddressDTOOne.getReceivedAddressAlias());
//        childOrderAddressDTOTwo.setSendStreet(childOrderAddressDTOOne.getReceivedStreet());
//        childOrderAddressDTOTwo.setSendDistrict(childOrderAddressDTOOne.getReceivedDistrict());
//        childOrderAddressDTOTwo.setSendDistrictCode(childOrderAddressDTOOne.getReceivedDistrictCode());
//        childOrderAddressDTOTwo.setSendTownship(childOrderAddressDTOOne.getReceivedTownship());
//        childOrderAddressDTOTwo.setSendTownshipCode(childOrderAddressDTOOne.getReceivedTownshipCode());
//        childOrderAddressDTOTwo.setSendLongitude(childOrderAddressDTOOne.getReceivedLongitude());
//        childOrderAddressDTOTwo.setSendLatitude(childOrderAddressDTOOne.getReceivedLatitude());

//        //卸货点信息  最后一个为卸货点 中间为途经点
//        childOrderAddressDTOTwo.setReceivedAssociationCode(orderAddress.getReceivedAssociationCode());
//        childOrderAddressDTOTwo.setReceivedPhone(orderAddress.getReceivedPhone());
//        childOrderAddressDTOTwo.setReceivedName(orderAddress.getReceivedName());
//        childOrderAddressDTOTwo.setReceivedProvince(orderAddress.getReceivedProvince());
//        childOrderAddressDTOTwo.setReceivedProvinceCode(orderAddress.getReceivedProvinceCode());
//        childOrderAddressDTOTwo.setReceivedCity(orderAddress.getReceivedCity());
//        childOrderAddressDTOTwo.setReceivedCityCode(orderAddress.getReceivedCityCode());
//        childOrderAddressDTOTwo.setReceivedDistrict(orderAddress.getReceivedDistrict());
//        childOrderAddressDTOTwo.setReceivedDistrictCode(orderAddress.getReceivedDistrictCode());
//        childOrderAddressDTOTwo.setReceivedTownship(orderAddress.getReceivedTownship());
//        childOrderAddressDTOTwo.setReceivedTownshipCode(orderAddress.getReceivedTownshipCode());
//        childOrderAddressDTOTwo.setReceivedAddressAlias(orderAddress.getReceivedAddressAlias());
//        childOrderAddressDTOTwo.setReceivedStreet(orderAddress.getReceivedStreet());
//        childOrderAddressDTOTwo.setReceivedLongitude(orderAddress.getReceivedLongitude());
//        childOrderAddressDTOTwo.setReceivedLatitude(orderAddress.getReceivedLatitude());
//
//        childTradeOrderTwoDTO.setOrderAddress(childOrderAddressDTOTwo);
//
//        childOrders.add(childTradeOrderTwoDTO);
        tradeOrderDTO.setChildOrders(childOrders);

//        //app下单中商品就是各种服务费
        List<TradeOrderItemDTO> tradeOrderItems = new ArrayList<>();


        TradeOrderItemDTO tradeOrderItemDTO = new TradeOrderItemDTO();
        tradeOrderItemDTO.setItemName("起步价");
        tradeOrderItemDTO.setItemType(EnumOrderItemType.SERVICE.getCode());
        tradeOrderItemDTO.setQuantity(5); //5公里
        tradeOrderItemDTO.setPrice(10);
        tradeOrderItemDTO.setItemCode("001");

        //扩展信息
        Map<String, Object> orderExtData = new HashMap<>();
        orderExtData.put("goodsCategory", "人民币");
        orderExtData.put("goodsUnit", "元");
        orderExtData.put("goodsPic", "[\"1ac772701b9844ceb3da6c6396e8787d\",\"07db3b9acee8452c872a7f09d95f6b15\"]");
        tradeOrderItemDTO.setItemExtData(JSON.toJSONString(orderExtData));

        tradeOrderItems.add(tradeOrderItemDTO);

        TradeOrderItemDTO tradeOrderItemDTOOne = new TradeOrderItemDTO();
        tradeOrderItemDTOOne.setItemName("超公里费");
        tradeOrderItemDTOOne.setItemType(EnumOrderItemType.SERVICE.getCode());
        tradeOrderItemDTOOne.setQuantity(5); //5公里
        tradeOrderItemDTOOne.setPrice(20);
        tradeOrderItemDTOOne.setItemCode("002");

        tradeOrderItems.add(tradeOrderItemDTOOne);


        TradeOrderItemDTO tradeOrderItemDTOTwo = new TradeOrderItemDTO();
        tradeOrderItemDTOTwo.setItemName("等待费");
        tradeOrderItemDTOTwo.setItemType(EnumOrderItemType.SERVICE.getCode());
        tradeOrderItemDTOTwo.setQuantity(5); //5分钟
        tradeOrderItemDTOTwo.setPrice(10);
        tradeOrderItemDTOTwo.setItemCode("003");

        tradeOrderItems.add(tradeOrderItemDTOTwo);

        tradeOrderDTO.setTradeOrderItems(tradeOrderItems);


        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);

        return order.getData();
    }



//    @Test
//    public void sendMQ() {
//
//        String tradeOrderNo = "2025052818294061476";
//        TradeOrderQueryOneDTO tradeOrderQueryOneDTO = new TradeOrderQueryOneDTO();
//        tradeOrderQueryOneDTO.setTradeOrderNo(tradeOrderNo);
//        TradeOrderVO tradeOrderVO = tradeOrderService.queryOneOrder(tradeOrderQueryOneDTO);
//        mqUtil.sendPlatformOrderMq("order_paid_create", tradeOrderVO, tradeOrderNo);
//        System.out.println("发送成功");
//    }
    @Test
    public void xxlSchedule() {
        xxlSchedule.closeOrderByExpiredTime();
    }


    @Test
    public void nodeUpdateOrderExtend() {


        TradeOrderVO tradeOrderVOR = this.getTradeOrderVO();

        BaseContext baseContext = new BaseContext();
        baseContext.setTradeOrderNo(tradeOrderVOR.getTradeOrderNo());
        baseContext.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        baseContext.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        baseContext.setChainId("test");
        baseContext.setChainName("订单更新扩展信息");
        baseContext.setNodeId("test");
        baseContext.setNodeName("订单更新扩展信息");

        JSONObject jsonObject = new JSONObject();

        jsonObject.fluentPut("goodsDesc.dd","eee");
        jsonObject.fluentPut("goodsDesc.cc","cc");
        jsonObject.fluentPut("goodsDesc.name.third","333");
        jsonObject.fluentPut("goodsDesc.name.four","444");

        baseContext.setJsonObject(jsonObject);

        tradeFlowService.test(baseContext);

    }



}
