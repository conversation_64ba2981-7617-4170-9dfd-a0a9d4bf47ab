package com.yuanshuo.platform.order.flow.context;

import cn.hutool.core.bean.BeanUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PayRefundContext extends BaseContext {

    private Integer amount;
    private String payChannel;
    private String paymentReceiptRefundNo;

    /**
     * 从BaseContext构造UpdateOrderStatusToPaidContext，继承BaseContext的所有属性值
     * @param baseContext 基础上下文
     */
    public PayRefundContext(BaseContext baseContext) {
        BeanUtil.copyProperties(baseContext, this);
    }


}
