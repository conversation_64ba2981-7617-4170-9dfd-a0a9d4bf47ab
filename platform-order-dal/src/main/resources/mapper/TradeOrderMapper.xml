<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.TradeOrderMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.TradeOrder">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_parent_type" jdbcType="BIGINT" property="parentType" />
    <result column="reserve_time" jdbcType="BIGINT" property="reserveTime" />
    <result column="parent_no" jdbcType="VARCHAR" property="parentNo" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="sub_biz_type" jdbcType="VARCHAR" property="subBizType" />
    <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="total_amount" jdbcType="INTEGER" property="totalAmount" />
    <result column="total_discount" jdbcType="INTEGER" property="totalDiscount" />
    <result column="total_paid" jdbcType="INTEGER" property="totalPaid" />
    <result column="total_payable" jdbcType="INTEGER" property="totalPayable" />
    <result column="total_refund" jdbcType="INTEGER" property="totalRefund" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="payment_status" jdbcType="VARCHAR" property="paymentStatus" />
    <result column="refund_status" jdbcType="VARCHAR" property="refundStatus" />
    <result column="fulfillment_status" jdbcType="VARCHAR" property="fulfillmentStatus" />
    <result column="order_expire_time" jdbcType="TIMESTAMP" property="orderExpireTime" />
    <result column="order_address_id" jdbcType="BIGINT" property="orderAddressId" />
    <result column="received_province_code" jdbcType="VARCHAR" property="receivedProvinceCode" />
    <result column="received_city_code" jdbcType="VARCHAR" property="receivedCityCode" />
    <result column="received_district_code" jdbcType="VARCHAR" property="receivedDistrictCode" />
    <result column="send_province_code" jdbcType="VARCHAR" property="sendProvinceCode" />
    <result column="send_city_code" jdbcType="VARCHAR" property="sendCityCode" />
    <result column="send_district_code" jdbcType="VARCHAR" property="sendDistrictCode" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="actual_order_type" jdbcType="BIGINT" property="actualOrderType" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
  </resultMap>

  <select id="selectStatisticsByQuery" parameterType="com.yuanshuo.platform.order.mapper.query.TradeOrderQuery" resultType="Map">
    SELECT count(*) as totalCount,sum(total_amount) as totalAmount,sum(total_paid) as totalPaid,sum(total_payable) as totalPayable
    FROM trade_order
    <where>
      <if test="receivedCityCode != null and receivedCityCode != ''">
        AND received_city_code = #{receivedCityCode}
      </if>
      <if test="orderCreateTimeStart != null">
        AND create_time &gt;= #{orderCreateTimeStart}
      </if>
      <if test="orderCreateTimeEnd != null">
        AND create_time &lt;= #{orderCreateTimeEnd}
      </if>
      <if test="orderExpireTimeStart != null">
        AND order_expire_time &gt;= #{orderExpireTimeStart}
      </if>
      <if test="orderExpireTimeEnd != null">
        AND order_expire_time &lt;= #{orderExpireTimeEnd}
      </if>
      <if test="tradeOrderNoLike != null and tradeOrderNoLike != ''">
        AND trade_order_no LIKE CONCAT('%', #{tradeOrderNoLike}, '%')
      </if>
      <if test="nickNameLike != null and nickNameLike != ''">
        AND nick_name LIKE CONCAT('%', #{nickNameLike}, '%')
      </if>
      <if test="userPhoneLike != null and userPhoneLike != ''">
        AND user_phone LIKE CONCAT('%', #{userPhoneLike}, '%')
      </if>
      <if test="status != null and status != ''">
        AND status = #{status}
      </if>
      <if test="orderType !=null and orderType != ''">
        AND order_type = #{orderType}
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.yuanshuo.platform.order.mapper.query.TradeOrderQuery" resultMap="BaseResultMap">
    SELECT *
    FROM trade_order
    <where>
      <if test="receivedCityCode != null and receivedCityCode != ''">
        AND received_city_code = #{receivedCityCode}
      </if>
      <if test="orderCreateTimeStart != null">
        AND create_time &gt;= #{orderCreateTimeStart}
      </if>
      <if test="orderCreateTimeEnd != null">
        AND create_time &lt;= #{orderCreateTimeEnd}
      </if>
      <if test="orderExpireTimeStart != null">
        AND order_expire_time &gt;= #{orderExpireTimeStart}
      </if>
      <if test="orderExpireTimeEnd != null">
        AND order_expire_time &lt;= #{orderExpireTimeEnd}
      </if>
      <if test="tradeOrderNoLike != null and tradeOrderNoLike != ''">
        AND trade_order_no LIKE CONCAT('%', #{tradeOrderNoLike}, '%')
      </if>
      <if test="nickNameLike != null and nickNameLike != ''">
        AND nick_name LIKE CONCAT('%', #{nickNameLike}, '%')
      </if>
      <if test="userPhoneLike != null and userPhoneLike != ''">
        AND user_phone LIKE CONCAT('%', #{userPhoneLike}, '%')
      </if>
      <if test="status != null and status != ''">
        AND status = #{status}
      </if>
      <if test="statusList !=null and statusList.size()>0">
        AND status IN
        <foreach collection="statusList" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>

      <if test="userId != null">
        AND user_id = #{userId}
      </if>
      <if test="orderType !=null and orderType != ''">
        AND order_type = #{orderType}
      </if>
      <if test="tradeOrderNoList !=null and tradeOrderNoList.size()">
        AND trade_order_no IN
        <foreach collection="tradeOrderNoList" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
  </select>

</mapper>