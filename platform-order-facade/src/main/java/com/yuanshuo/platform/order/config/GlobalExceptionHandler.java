package com.yuanshuo.platform.order.config;

import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.common.BusinessException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    public static String getErrorMsg(BindingResult bindingResult) {
        StringBuilder sb = new StringBuilder(32);
        List<ObjectError> list = bindingResult.getAllErrors();
        for (ObjectError objectError : list) {
            if (objectError instanceof FieldError) {
                FieldError fieldError = (FieldError) objectError;
                sb.append(fieldError.getField());
            } else {

            }
            sb.append(objectError.getDefaultMessage()).append(";");
        }
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1);
        }

        return sb.toString();
    }

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public R handlerBindException(BindException e, HttpServletRequest request) {
        log.error("url:{}", request.getRequestURI(), e);
        return R.fail(getErrorMsg(e));
    }

    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public R handlerBusinessException(Exception e, HttpServletRequest request) {
        log.error("url:{}", request.getRequestURI(), e);
        return  R.fail(e.getMessage());
    }

    @ExceptionHandler()
    @ResponseBody
    public R handlerException(Exception e, HttpServletRequest request) {
        log.error("url:{}", request.getRequestURI(), e);
        return R.fail("服务器内部异常");
    }

}
