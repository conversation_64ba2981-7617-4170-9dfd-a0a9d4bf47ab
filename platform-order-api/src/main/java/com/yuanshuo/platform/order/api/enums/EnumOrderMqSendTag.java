package com.yuanshuo.platform.order.api.enums;

public enum EnumOrderMqSendTag {

    ORDER_CANCEL("order_cancel", "订单取消事件"),
    ORDER_CREATE("order_create", "订单创建事件"),
    ORDER_PAID("order_paid", "订单付款事件"),
    //订单改价成功
    ORDER_PRICE_CHANGE_SUCCESS("order_price_change_success", "订单改价成功"),
    ;
    private String tag;
    private String desc;

    private EnumOrderMqSendTag(String tag, String desc) {
        this.tag = tag;
        this.desc = desc;
    }

    public String getTag() {
        return this.tag;
    }

    public String getDesc() {
        return this.desc;
    }
    //根据code获取枚举
    public static EnumOrderMqSendTag getByCode(String code) {
        for (EnumOrderMqSendTag value : values()) {
            if (value.tag.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
