package com.yuanshuo.platform.order.schedule;

import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.platform.order.api.enums.EnumOperatorType;
import com.yuanshuo.platform.order.api.enums.EnumOrderPaymentStatus;
import com.yuanshuo.platform.order.api.enums.EnumTradeOrderStatus;
import com.yuanshuo.platform.order.api.model.dto.CloseTradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.query.TradeOrderQueryPageDTO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.server.TradeOrderService;
import com.yuanshuo.platform.order.server.flow.TradeFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
@EnableScheduling
public class XxlSchedule {
    @Autowired
    TradeOrderService tradeOrderService;
    @Autowired
    private TradeFlowService tradeFlowService;


    @XxlJob("closeOrderByExpiredTime")
    public ReturnT<String> closeOrderByExpiredTime() {
        log.info("订单过期定时任务开始");
        //查询并刷新状态
        TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
        tradeOrderQueryPageDTO.setStatusList(Lists.newArrayList(EnumTradeOrderStatus.PAYING.getCode(), EnumTradeOrderStatus.INITIALIZE.getCode()));
        tradeOrderQueryPageDTO.setOrderExpireTimeEnd(new Date());
        tradeOrderQueryPageDTO.setPageNo(1);
        tradeOrderQueryPageDTO.setPageSize(100);
        TableDataInfo<TradeOrderVO> tradeOrderVOTableDataInfo = tradeOrderService.queryPageOrder(tradeOrderQueryPageDTO);
        for (TradeOrderVO row : tradeOrderVOTableDataInfo.getRows()) {
            String tradeOrderNo = row.getTradeOrderNo();
            try {
                log.info("订单过期定时任务处理,tradeOrderNo{}",row.getTradeOrderNo());
                if(row.getStatus().equals(EnumTradeOrderStatus.INITIALIZE.getCode()) || row.getPaymentStatus().equals(EnumOrderPaymentStatus.PENDING_PAYMENT.getCode())){
                    CloseTradeOrderDTO closeTradeOrderDTO = new CloseTradeOrderDTO();
                    closeTradeOrderDTO.setTradeOrderNo(tradeOrderNo);
                    closeTradeOrderDTO.setNeedAudit(false);
                    closeTradeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
                    closeTradeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
                    tradeFlowService.cancelOrderForTimeOut(closeTradeOrderDTO);
                }
            } catch (Exception e) {
                log.error("订单过期定时任务异常,tradeOrderNo{}",tradeOrderNo, e);
            }

        }

        return  ReturnT.SUCCESS;
    }


}
