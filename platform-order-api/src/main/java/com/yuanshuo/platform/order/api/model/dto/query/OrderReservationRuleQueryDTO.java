package com.yuanshuo.platform.order.api.model.dto.query;

import com.yuanshuo.common.entity.support.PageQuery;
import lombok.Data;

import java.util.Date;

/**
 * 预约下单规则查询DTO
 */
@Data
public class OrderReservationRuleQueryDTO extends PageQuery {
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 城市编码
     */
    private String cityCode;
    
    /**
     * 规则状态（0: 禁用, 1: 启用）
     */
    private Integer status;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 创建时间开始
     */
    private Date createTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
    
}