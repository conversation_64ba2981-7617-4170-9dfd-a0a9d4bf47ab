<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderReservationRuleMapper">
    
    <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderReservationRule">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
        <result column="city_codes" jdbcType="VARCHAR" property="cityCodes" />
        <result column="future_days" jdbcType="INTEGER" property="futureDays" />
        <result column="time_interval" jdbcType="INTEGER" property="timeInterval" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, rule_id, city_codes, future_days, time_interval, status, remark, 
        is_delete, create_user, create_time, update_time
    </sql>
    
    <!-- 根据查询条件查询规则列表 -->
    <select id="selectByQuery" parameterType="com.yuanshuo.platform.order.mapper.query.OrderReservationRuleQuery" 
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM order_reservation_rule
        <where>
            <if test="ruleId != null and ruleId != ''">
                AND rule_id = #{ruleId}
            </if>
            <if test="cityCode != null and cityCode != ''">
                AND FIND_IN_SET(#{cityCode}, city_codes) > 0
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="createUser != null and createUser != ''">
                AND create_user = #{createUser}
            </if>
            <if test="createTimeStart != null">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND create_time <= #{createTimeEnd}
            </if>
            <if test="isDelete != null">
                AND is_delete = #{isDelete}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据城市编码查询规则 -->
    <select id="selectByCityCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM order_reservation_rule
        WHERE FIND_IN_SET(#{cityCode}, city_codes) > 0
        AND status = 1
        AND is_delete = 0
        LIMIT 1
    </select>
    
    <!-- 根据规则ID查询规则 -->
    <select id="selectByRuleId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM order_reservation_rule
        WHERE rule_id = #{ruleId}
        AND is_delete = 0
        LIMIT 1
    </select>
    
</mapper>