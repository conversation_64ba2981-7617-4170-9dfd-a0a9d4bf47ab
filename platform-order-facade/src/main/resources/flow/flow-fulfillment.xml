<flow>
    <!--只包括跟履约有关的编排文件-->
    <!-- 履约服务运输单创建回调 -->
    <chain name="callbackFulfillmentCreateForAppPlaceOrder">
        cmpData = '[{"status":"paying","process":"nodeUpdateOrderStatusToFulfilling"}]';
        cmpDataFulfillment = '[{"fulfillmentStatus":null,"process":"nodeUpdateFulfillmentStatusToDispatchPending"}]';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            ) .to(
                nodeUpdateOrderStatusToFulfilling
            ),
            SWITCH(
                switchFulfillmentStatusToProcess.bind("switchFulfillmentStatus",cmpDataFulfillment)
            ) .to(
                nodeUpdateFulfillmentStatusToDispatchPending
            ),
            syncStatus
        );
    </chain>
    <!-- 履约服务运输中回调 -->
    <chain name="callbackFulfillmentTransportForAppPlaceOrder">
        cmpData = '[{"fulfillmentStatus":"transiting","process":"nodeWarnProcess"},{"fulfillmentStatus":"dispatch_pending","process":"nodeUpdateFulfillmentStatusToTransiting"}]';
        THEN(
            SWITCH(
                switchFulfillmentStatusToProcess.bind("switchFulfillmentStatus",cmpData)
            )
            .to(
                nodeUpdateFulfillmentStatusToTransiting,
                nodeWarnProcess.bind("msg","履约服务运输中回调告警")
            ),
            syncStatus
        );
    </chain>
    <!-- 履约服务已完成回调 -->
    <chain name="callbackFulfillmentCompleteForAppPlaceOrder">
        cmpData = '[{"status":"fulfilling","process":"nodeUpdateOrderStatusToCompleted"}]';
        cmpDataFulfillment = '[{"fulfillmentStatus":"dispatch_pending","process":"nodeUpdateFulfillmentStatusToSuccess"},{"fulfillmentStatus":"transiting","process":"nodeUpdateFulfillmentStatusToSuccess"}]';
        THEN(
            SWITCH(
                switchFulfillmentStatusToProcess.bind("switchFulfillmentStatus",cmpDataFulfillment)
            )
            .to(
                nodeUpdateFulfillmentStatusToSuccess
            ),
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            ) .to(
                nodeUpdateOrderStatusToCompleted
            ),
            syncStatus
        );
    </chain>
</flow>
