package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.*;
import java.util.Date;
import lombok.Data;

/**
 * 表名：order_operation_log
 * 表注释：通用操作日志表
*/
@Data
@Table(name = "order_operation_log")
public class OrderOperationLog {
    /**
     * 主键ID（自增）
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 订单编号
     */
    @Column(name = "trade_order_no")
    private String tradeOrderNo;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名称（冗余字段）
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 操作人ID
     */
    @Column(name = "operator_user_id")
    private Long operatorUserId;

    /**
     * 操作人名称
     */
    @Column(name = "operator_user_name")
    private String operatorUserName;

    /**
     * 操作时间
     */
    @Column(name = "operator_time")
    private Date operatorTime;


    /**
     * 操作节点id
     */
    @Column(name = "node_id")
    private String nodeId;

    /**
     * 操作节点名称
     */
    @Column(name = "node_name")
    private String nodeName;

    /**
     * 操作流程的ID
     */
    @Column(name = "chain_id")
    private String chainId;

    /**
     * 操作流程的名称
     */
    @Column(name = "chain_name")
    private String chainName;


    /**
     * 全链路追踪ID
     */
    @Column(name = "trace_id")
    private String traceId;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 变更前完整数据快照
     */
    @Column(name = "before_data")
    private String beforeData;

    /**
     * 变更后完整数据快照
     */
    @Column(name = "after_data")
    private String afterData;
}