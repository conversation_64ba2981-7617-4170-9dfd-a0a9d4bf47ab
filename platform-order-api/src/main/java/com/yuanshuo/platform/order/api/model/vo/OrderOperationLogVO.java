package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

import java.util.Date;

@Data
public class OrderOperationLogVO {

    /**
     * 主键ID（自增）
     */
    private Long id;

    /**
     * 订单编号
     */
    private String tradeOrderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称（冗余字段）
     */
    private String userName;

    /**
     * 操作人ID
     */
    private Long operatorUserId;

    /**
     * 操作人名称
     */
    private String operatorUserName;

    /**
     * 操作时间
     */
    private Date operatorTime;


    /**
     * 操作节点id
     */
    private String nodeId;

    /**
     * 操作节点名称
     */
    private String nodeName;

    /**
     * 操作流程的ID
     */
    private String chainId;

    /**
     * 操作流程的名称
     */
    private String chainName;

    /**
     * 全链路追踪ID
     */
    private String traceId;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    private String createUser;

    private Date createTime;

    private Date updateTime;

    /**
     * 变更前完整数据快照
     */
    private String beforeData;

    /**
     * 变更后完整数据快照
     */
    private String afterData;
}
