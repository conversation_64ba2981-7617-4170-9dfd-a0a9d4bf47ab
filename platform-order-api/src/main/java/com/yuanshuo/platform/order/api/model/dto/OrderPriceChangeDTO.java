package com.yuanshuo.platform.order.api.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单改价请求DTO
 */
@Data
public class OrderPriceChangeDTO extends BaseDTO {
    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String tradeOrderNo;

    /**
     * 改价后总金额（单位：分）
     */
    @NotNull(message = "改价后总金额不能为空")
    private Integer afterTotalAmount;

    /**
     * 改价后总优惠金额（单位：分）
     */
    @NotNull(message = "改价后总金额不能为空")
    private Integer afterTotalDiscount = 0;
    

    /**
     * 改价原因
     */
    private String changeReason;


}