<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.AdministrativeDivisionMapper">
    
    <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.AdministrativeDivision">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="longitude" jdbcType="DOUBLE" property="longitude" />
        <result column="latitude" jdbcType="DOUBLE" property="latitude" />
        <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
        <result column="level" jdbcType="VARCHAR" property="level" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, code, name, longitude, latitude, parent_code, level, 
        create_time, update_time, is_delete
    </sql>
    
    <!-- 根据编码查询行政区划 -->
    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM administrative_division
        WHERE code = #{code}
        AND is_delete = 0
    </select>
    
    <!-- 模糊查询城市（按名称） -->
    <select id="selectByNameLike" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM administrative_division
        WHERE is_delete = 0
        AND name LIKE CONCAT('%', #{name}, '%')
        <if test="level != null and level != ''">
            AND level = #{level}
        </if>
        ORDER BY level, name
    </select>
    
    <!-- 批量根据编码查询行政区划 -->
    <select id="selectByCodes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM administrative_division
        WHERE is_delete = 0
        AND code IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    
    <!-- 查询所有行政区划（支持分页） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM administrative_division
        WHERE is_delete = 0
        <if test="level != null and level != ''">
            AND level = #{level}
        </if>
        ORDER BY level, name
    </select>
    
</mapper>