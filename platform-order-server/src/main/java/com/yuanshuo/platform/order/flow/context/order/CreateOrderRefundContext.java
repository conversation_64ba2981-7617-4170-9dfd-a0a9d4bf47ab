package com.yuanshuo.platform.order.flow.context.order;

import cn.hutool.core.bean.BeanUtil;
import com.yuanshuo.platform.order.api.model.dto.OrderRefundDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CreateOrderRefundContext extends BaseContext {

    /**
     * 创建退款单的参数
     */
    private OrderRefundDTO orderRefundDTO;

    /**
     * 创建完退款单的返回参数
     */
    private OrderRefundVO orderRefundVO;

    /**
     * 从BaseContext构造CreateOrderRefundContext，继承BaseContext的所有属性值
     * @param baseContext 基础上下文
     */
    public CreateOrderRefundContext(BaseContext baseContext) {
        BeanUtil.copyProperties(baseContext, this);
    }

    /**
     * 从BaseContext构造CreateOrderRefundContext，并设置orderRefundDTO
     * @param baseContext 基础上下文
     * @param orderRefundDTO 退款单DTO
     */
    public CreateOrderRefundContext(BaseContext baseContext, OrderRefundDTO orderRefundDTO) {
        BeanUtil.copyProperties(baseContext, this);
        this.orderRefundDTO = orderRefundDTO;
    }

}
