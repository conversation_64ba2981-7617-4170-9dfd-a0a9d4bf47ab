package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * 表名：trade_order
 * 表注释：订单主表
*/
@Data
@Table(name = "trade_order")
public class TradeOrder {
    /**
     * 主键ID（自增）
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;



    /**
     * 是否父订单
     * true 是 父订单
     * false 是子订单
     * 空则当前订单没有父子订单关系
     */
    @Column(name = "is_parent_type")
    private Boolean parentType;


    /**
     * 预定履约时间
     */
    @Column(name = "reserve_time")
    private Date reserveTime;
    /**
     * 父订单id（指向本表id）
     */
    @Column(name = "parent_no")
    private String parentNo;

    /**
     * 订单编号
     */
    @Column(name = "trade_order_no")
    private String tradeOrderNo;

    /**
     * 订单业务类型,如销售小工具
     */
    @Column(name = "order_type")
    private String orderType;

    /**
     * 业务类型,如运输
     */
    @Column(name = "biz_type")
    private String bizType;

    /**
     * 业务子类型,如无人车
     */
    @Column(name = "sub_biz_type")
    private String subBizType;

    /**
     * 渠道类型,支付宝小程序/微信小程序等等
     */
    @Column(name = "channel_type")
    private String channelType;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "nick_name")
    private String nickName;

    /**
     * 联系电话
     */
    @Column(name = "user_phone")
    private String userPhone;

    /**
     * 总金额（主订单有效）,单位分
     */
    @Column(name = "total_amount")
    private Integer totalAmount;

    /**
     * 总优惠金额,单位分
     */
    @Column(name = "total_discount")
    private Integer totalDiscount;

    /**
     * 总实付金额,单位分
     */
    @Column(name = "total_paid")
    private Integer totalPaid;
    
    /**
     * 总应付金额,单位分
     */
    @Column(name = "total_payable")
    private Integer totalPayable;

    /**
     * 总退款金额,单位分
     */
    @Column(name = "total_refund")
    private Integer totalRefund;

    /**
     * 订单总状态
     */
    private String status;

    /**
     * 支付状态
     */
    @Column(name = "payment_status")
    private String paymentStatus;

    /**
     * 退款状态
     */
    @Column(name = "refund_status")
    private String refundStatus;

    /**
     * 履约状态
     */
    @Column(name = "fulfillment_status")
    private String fulfillmentStatus;
    
    /**
     * 订单超时时间
     */
    @Column(name = "order_expire_time")
    private Date orderExpireTime;

    /**
     * 地址编号
     */
    @Column(name = "order_address_id")
    private Long orderAddressId;

    /**
     * 收件省份/直辖市code
     */
    @Column(name = "received_province_code")
    private String receivedProvinceCode;

    /**
     * 收件城市code
     */
    @Column(name = "received_city_code")
    private String receivedCityCode;

    /**
     * 收件区/县code
     */
    @Column(name = "received_district_code")
    private String receivedDistrictCode;

    /**
     * 发件省份/直辖市code
     */
    @Column(name = "send_province_code")
    private String sendProvinceCode;

    /**
     * 发件城市code
     */
    @Column(name = "send_city_code")
    private String sendCityCode;

    /**
     * 发件区/县code
     */
    @Column(name = "send_district_code")
    private String sendDistrictCode;

    /**
     * 是否删除（0: 未删除, 1: 已删除）
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否实时单 1-是 0-否
     * true 是 实时单
     * false 是非实时单
     */
    @Column(name = "actual_order_type")
    private Boolean actualOrderType;


    /**
     * 支付渠道
     */
    @Column(name = "pay_channel")
    private String payChannel;
}