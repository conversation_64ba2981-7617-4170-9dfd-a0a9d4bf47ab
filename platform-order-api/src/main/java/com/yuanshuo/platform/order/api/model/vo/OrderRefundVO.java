package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

import java.util.Date;
@Data
public class OrderRefundVO {

    private Long id;

    /**
     * 订单no
     */
    private String tradeOrderNo;

    /**
     * 订单退款单no
     */
    private String orderRefundNo;
    
    /**
     * 改价单单号
     */
    private String priceChangeNo;

    /**
     * 退款金额
     */
    private Integer totalAmount;

    /**
     * 实退金额
     */
    private Integer receivedAmount;


    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
