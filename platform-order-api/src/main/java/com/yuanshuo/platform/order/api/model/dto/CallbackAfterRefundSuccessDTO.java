package com.yuanshuo.platform.order.api.model.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CallbackAfterRefundSuccessDTO extends BaseDTO{
    /**
     * 订单退款单号
     */
    //todo 按理来说不该有退款单号,所有的都应该通过退款单关联查询
    private String tradeOrderNo;
    /**
     * 业务退款单单号
     */
    private String orderRefundNo;

    @NotNull
    private Integer amount;

    private String paymentReceiptRefundNo;

    private String payChannel;

}
