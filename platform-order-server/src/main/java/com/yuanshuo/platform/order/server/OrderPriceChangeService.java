package com.yuanshuo.platform.order.server;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.yuanshuo.platform.order.api.enums.*;
import com.yuanshuo.platform.order.api.model.dto.OrderPriceChangeDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderPriceChangeVO;
import com.yuanshuo.platform.order.common.BusinessException;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.flow.context.PayPaidContext;
import com.yuanshuo.platform.order.flow.context.PayRefundContext;
import com.yuanshuo.platform.order.mapper.OrderPriceChangeMapper;
import com.yuanshuo.platform.order.mapper.po.OrderPriceChange;
import com.yuanshuo.platform.order.mapper.po.TradeOrder;
import com.yuanshuo.platform.order.util.LockExecutorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单改价服务
 */
@Service
@Slf4j
public class OrderPriceChangeService {

    @Autowired
    private TradeOrderService tradeOrderService;

    @Autowired
    private OrderPriceChangeMapper orderPriceChangeMapper;

    @Autowired
    private LockExecutorUtil lockExecutorUtil;

    /**
     * 订单改价申请
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderPriceChangeVO changeOrderPrice(OrderPriceChangeDTO priceChangeDTO, BaseContext baseContext) {
        String tradeOrderNo = priceChangeDTO.getTradeOrderNo();
        // 使用订单锁保证并发安全
      return  lockExecutorUtil.executeWithLock(tradeOrderNo, 5, 10, () -> {
            // 1. 校验订单状态和改价权限
            validateOrderForPriceChange(tradeOrderNo);
            // 2. 获取原始订单
            TradeOrder originalOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
            // 4. 创建改价记录
            OrderPriceChange priceChangeRecord = createPriceChangeRecord(priceChangeDTO, originalOrder, baseContext);
            orderPriceChangeMapper.insertSelective(priceChangeRecord);
            // 5. 自动审核通过，更新订单金额
            TradeOrder updatedOrder = new TradeOrder();
            updatedOrder.setTradeOrderNo(tradeOrderNo);
            updatedOrder.setTotalAmount(priceChangeDTO.getAfterTotalAmount());
            updatedOrder.setTotalDiscount(priceChangeDTO.getAfterTotalDiscount());
            updatedOrder.setTotalPayable(priceChangeRecord.getAfterTotalPayable());
            // 6. 使用现有机制更新订单（会自动记录操作日志和状态历史）
            tradeOrderService.updateOrderByTradeOrder(updatedOrder, baseContext);
            log.info("订单改价成功，订单号：{}，改价前金额：{}，改价后金额：{}",
                    tradeOrderNo, originalOrder.getTotalAmount(), updatedOrder.getTotalAmount());
            OrderPriceChangeVO orderPriceChangeVO = BeanUtil.copyProperties(priceChangeRecord, OrderPriceChangeVO.class);
            return orderPriceChangeVO;
        });
    }

    /**
     * 校验订单是否可以改价
     */
    private void validateOrderForPriceChange(String tradeOrderNo) {
        TradeOrder order = tradeOrderService.queryByOrderNoMust(tradeOrderNo);


        // 检查是否存在处理中或待处理的资金状态改价单
        List<OrderPriceChange> processingChanges = orderPriceChangeMapper.selectByTradeOrderNoAndFundProcessStatusIn(tradeOrderNo,
                Arrays.asList(EnumFundProcessStatus.PROCESSING.getCode(), EnumFundProcessStatus.PENDING.getCode()));

        if (!processingChanges.isEmpty()) {
            throw new BusinessException("订单存在处理中或待处理的改价资金，不允许再次改价！");
        }

        // 检查是否已经存在改价记录，不允许多次改价
        List<OrderPriceChange> existingChanges = orderPriceChangeMapper.selectByTradeOrderNo(tradeOrderNo);
        if (!existingChanges.isEmpty()) {
            throw new BusinessException("订单已经改过价，不允许多次改价！");
        }

        // 可以根据需要添加其他校验逻辑，如订单状态、用户权限等
    }

    /**
     * 创建改价记录
     */
    private OrderPriceChange createPriceChangeRecord(OrderPriceChangeDTO priceChangeDTO, TradeOrder originalOrder, BaseContext baseContext) {
        OrderPriceChange priceChange = new OrderPriceChange();
        // 基本信息
        priceChange.setTradeOrderNo(priceChangeDTO.getTradeOrderNo());
        // 生成改价单号：日期时间(yyyyMMddHHmmss) + 5位随机数字
        String priceChangeNo = "PC" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(5);
        priceChange.setPriceChangeNo(priceChangeNo);
        priceChange.setChangeReason(priceChangeDTO.getChangeReason());
        // 判断改价类型（根据支付状态）
        String paymentStatus = originalOrder.getPaymentStatus();
        if (EnumOrderPaymentStatus.PAID.getCode().equals(paymentStatus)) {
            priceChange.setChangeType(EnumPriceChangeType.AFTER_PAYMENT.getCode());
        } else {
            priceChange.setChangeType(EnumPriceChangeType.BEFORE_PAYMENT.getCode());
        }
        priceChange.setPaymentStatus(paymentStatus);
        // 改价前金额（单位：分）
        priceChange.setBeforeTotalAmount(originalOrder.getTotalAmount());
        priceChange.setBeforeTotalDiscount(originalOrder.getTotalDiscount());
        priceChange.setBeforeTotalPayable(originalOrder.getTotalPayable());
        // 改价后金额（单位：分）
        priceChange.setAfterTotalAmount(priceChangeDTO.getAfterTotalAmount());
        priceChange.setAfterTotalDiscount(priceChangeDTO.getAfterTotalDiscount());
        priceChange.setAfterTotalPayable(priceChangeDTO.getAfterTotalAmount()-priceChangeDTO.getAfterTotalDiscount());
        // 计算差额（单位：分）
        Integer difference = priceChange.getAfterTotalPayable() - priceChange.getBeforeTotalPayable();
        priceChange.setAmountDifference(difference);

        // 根据差额设置资金处理类型和状态
        if (EnumPriceChangeType.BEFORE_PAYMENT.getCode().equals(priceChange.getChangeType())) {
            // 支付前改价，无需处理资金变动
            priceChange.setFundProcessType(EnumFundProcessType.NONE.getCode());
            priceChange.setFundProcessAmount(0);
            priceChange.setFundProcessStatus(EnumFundProcessStatus.NOT_REQUIRED.getCode());
        } else if (difference > 0) {
            // 补款
            priceChange.setFundProcessType(EnumFundProcessType.SUPPLEMENT.getCode());
            priceChange.setFundProcessAmount(difference);
            priceChange.setFundProcessStatus(EnumFundProcessStatus.PENDING.getCode());
        } else if (difference < 0) {
            // 退款
            priceChange.setFundProcessType(EnumFundProcessType.REFUND.getCode());
            priceChange.setFundProcessAmount(Math.abs(difference));
            priceChange.setFundProcessStatus(EnumFundProcessStatus.PENDING.getCode());
        } else {
            // 无需处理
            priceChange.setFundProcessType(EnumFundProcessType.NONE.getCode());
            priceChange.setFundProcessAmount(0);
            priceChange.setFundProcessStatus(EnumFundProcessStatus.NOT_REQUIRED.getCode());
        }

        // 改价状态（自动通过）
        priceChange.setChangeStatus("approved");
        priceChange.setApprovalStatus(EnumPriceChangeApprovalStatus.AUTO_APPROVED.getCode());
        priceChange.setApprovalTime(new Date());
        // 操作人信息
        priceChange.setCreateUser(baseContext.getOperatorUserName());
        priceChange.setCreateTime(new Date());
        return priceChange;
    }

    /**
     * 查询订单改价历史
     */
    public List<OrderPriceChangeVO> getPriceChangeHistory(String tradeOrderNo) {
        List<OrderPriceChange> priceChangeList = orderPriceChangeMapper.selectByTradeOrderNo(tradeOrderNo);

        return priceChangeList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 获取订单最新改价记录
     */
    public OrderPriceChangeVO getLatestPriceChange(String tradeOrderNo) {
        OrderPriceChange latestChange = orderPriceChangeMapper.selectLatestByTradeOrderNo(tradeOrderNo);
        return Objects.nonNull(latestChange) ? convertToVO(latestChange) : null;
    }

    /**
     * 转换为VO对象
     */
    private OrderPriceChangeVO convertToVO(OrderPriceChange priceChange) {
        OrderPriceChangeVO vo = BeanUtil.copyProperties(priceChange, OrderPriceChangeVO.class);
        return vo;
    }

    /**
     * 根据改价单号查询订单改价记录
     *
     * @param priceChangeNo 改价单号
     * @return 订单改价记录
     * @return
     */
    public OrderPriceChange getByPriceChangeNo(String priceChangeNo) {
        return orderPriceChangeMapper.selectByPriceChangeNo(priceChangeNo);
    }
    /**
     * 根据改价单号修改订单改价记录
     *
     * @param priceChange 包含更新内容的订单改价对象
     * @return 更新后的订单改价记录
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderPriceChange updateByPriceChangeNo(OrderPriceChange priceChange) {
        if (priceChange == null || priceChange.getPriceChangeNo() == null) {
            throw new BusinessException("改价单号不能为空");
        }

        // 查询原记录
        String priceChangeNo = priceChange.getPriceChangeNo();
        OrderPriceChange originalRecord = orderPriceChangeMapper.selectByPriceChangeNo(priceChangeNo);
        if (originalRecord == null) {
            throw new BusinessException("未找到改价单号为" + priceChangeNo + "的记录");
        }

        // 保留原记录的ID和改价单号
        priceChange.setId(originalRecord.getId());

        // 设置更新时间
        priceChange.setUpdateTime(new Date());

        // 更新记录
        orderPriceChangeMapper.updateByPrimaryKeySelective(priceChange);

        // 返回更新后的完整记录
        return orderPriceChangeMapper.selectByPriceChangeNo(priceChangeNo);
    }

    /**
     * 更新改价单为已支付
     * 
     * @param payPaidContext 支付成功上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePriceChangeToPaid(PayPaidContext payPaidContext) {
        String tradeOrderNo = payPaidContext.getTradeOrderNo();
        log.info("更新改价单为已支付状态，订单号：{}", tradeOrderNo);
        
        // 查询该订单最新的改价记录
        OrderPriceChange latestChange = orderPriceChangeMapper.selectLatestByTradeOrderNo(tradeOrderNo);
        if (latestChange == null) {
            log.warn("未找到订单{}的改价记录，无需更新", tradeOrderNo);
            return;
        }
        
        // 只处理需要资金处理的改价单
        if (EnumFundProcessType.NONE.getCode().equals(latestChange.getFundProcessType()) ||
            EnumFundProcessStatus.NOT_REQUIRED.getCode().equals(latestChange.getFundProcessStatus())) {
            log.info("订单{}的改价单{}无需资金处理，跳过更新", tradeOrderNo, latestChange.getPriceChangeNo());
            return;
        }
        
        // 只处理待处理或处理中的资金状态
        if (!EnumFundProcessStatus.PENDING.getCode().equals(latestChange.getFundProcessStatus()) &&
            !EnumFundProcessStatus.PROCESSING.getCode().equals(latestChange.getFundProcessStatus())) {
            log.info("订单{}的改价单{}资金处理状态为{}，不需要更新", 
                    tradeOrderNo, latestChange.getPriceChangeNo(), latestChange.getFundProcessStatus());
            return;
        }
        
        // 更新改价单资金处理状态为成功
        OrderPriceChange updateChange = new OrderPriceChange();
        updateChange.setId(latestChange.getId());
        updateChange.setFundProcessStatus(EnumFundProcessStatus.SUCCESS.getCode());
        updateChange.setFundProcessTime(new Date());
        updateChange.setFundProcessNo(payPaidContext.getPaymentReceiptNo());
        
        // 如果是补款类型，记录实际支付金额
        if (EnumFundProcessType.SUPPLEMENT.getCode().equals(latestChange.getFundProcessType())) {
            updateChange.setFundProcessAmount(payPaidContext.getAmount());
        }
        
        // 更新改价单
        orderPriceChangeMapper.updateByPrimaryKeySelective(updateChange);
        log.info("成功更新改价单{}的资金处理状态为成功", latestChange.getPriceChangeNo());
        
        // 暂时先不更新订单金额
    }
    /**
     * 更新改价单为已退款
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePriceChangeToRefunded(PayRefundContext payRefundContext) {
        String tradeOrderNo = payRefundContext.getTradeOrderNo();
        log.info("更新改价单为已退款状态，订单号：{}", tradeOrderNo);
        
        // 查询该订单最新的改价记录
        OrderPriceChange latestChange = orderPriceChangeMapper.selectLatestByTradeOrderNo(tradeOrderNo);
        if (latestChange == null) {
            log.warn("未找到订单{}的改价记录，无需更新", tradeOrderNo);
            return;
        }
        
        // 只处理需要资金处理的改价单
        if (EnumFundProcessType.NONE.getCode().equals(latestChange.getFundProcessType()) ||
            EnumFundProcessStatus.NOT_REQUIRED.getCode().equals(latestChange.getFundProcessStatus())) {
            log.info("订单{}的改价单{}无需资金处理，跳过更新", tradeOrderNo, latestChange.getPriceChangeNo());
            return;
        }
        
        // 只处理待处理或处理中的资金状态
        if (!EnumFundProcessStatus.PENDING.getCode().equals(latestChange.getFundProcessStatus()) &&
            !EnumFundProcessStatus.PROCESSING.getCode().equals(latestChange.getFundProcessStatus())) {
            log.info("订单{}的改价单{}资金处理状态为{}，不需要更新", 
                    tradeOrderNo, latestChange.getPriceChangeNo(), latestChange.getFundProcessStatus());
            return;
        }
        
        // 更新改价单资金处理状态为成功
        OrderPriceChange updateChange = new OrderPriceChange();
        updateChange.setId(latestChange.getId());
        updateChange.setFundProcessStatus(EnumFundProcessStatus.SUCCESS.getCode());
        updateChange.setFundProcessTime(new Date());
        updateChange.setFundProcessNo(payRefundContext.getPaymentReceiptRefundNo());
        
        // 如果是退款类型，记录实际退款金额
        if (EnumFundProcessType.REFUND.getCode().equals(latestChange.getFundProcessType())) {
            updateChange.setFundProcessAmount(payRefundContext.getAmount());
        }
        
        // 更新改价单
        orderPriceChangeMapper.updateByPrimaryKeySelective(updateChange);
        log.info("成功更新改价单{}的资金处理状态为成功", latestChange.getPriceChangeNo());
    }
}