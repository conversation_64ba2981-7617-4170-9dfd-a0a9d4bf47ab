package com.yuanshuo.platform.order.util;

import cn.hutool.json.JSONUtil;

public class DeepCopyUtil {

    /**
     * 使用 JSON 序列化和反序列化实现对象的深拷贝
     * @param source 源对象
     * @param tClass
     * @return
     * @param <T>
     */
    public static <T> T deepCopyByJson(T source ,Class<T> tClass) {
        if (source == null) {
            return null;
        }
        String jsonStr = JSONUtil.toJsonStr(source);
        return JSONUtil.toBean(jsonStr, tClass);
    }

}
