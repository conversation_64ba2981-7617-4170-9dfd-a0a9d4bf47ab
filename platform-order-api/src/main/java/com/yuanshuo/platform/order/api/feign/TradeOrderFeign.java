package com.yuanshuo.platform.order.api.feign;

import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.model.dto.AuditTradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.CloseTradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.OrderRefundDTO;
import com.yuanshuo.platform.order.api.model.dto.TradeOrderDTO;
import com.yuanshuo.platform.order.api.model.dto.query.*;
import com.yuanshuo.platform.order.api.model.vo.OrderOperationLogVO;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderStatisticsVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * 订单统一入口
 */
@FeignClient(value = "platform-order")
public interface TradeOrderFeign {
    /**
     * 下单接口
     */
    @RequestMapping(value = "/order/createOrder",method = RequestMethod.POST)
    public R<TradeOrderVO> createOrder(@RequestBody TradeOrderDTO tradeOrderDTO);



    /**
     * 用户取消接口
     *
     */
    @RequestMapping(value = "/order/cancelOrderByUser",method = RequestMethod.POST)
    public  R<Void> cancelOrderByUser(@RequestBody CloseTradeOrderDTO closeOrderDTO);


    /**
     * 取消接口运营后台
     *
     */
    @RequestMapping(value = "/order/cancelOrderByOperations",method = RequestMethod.POST)
    public  R<Void> cancelOrderByOperations(@RequestBody CloseTradeOrderDTO closeOrderDTO);
    /**
     * 查询接口
     */
    @RequestMapping(value = "/order/queryPageOrder",method = RequestMethod.POST)
    public R<TableDataInfo<TradeOrderVO>> queryPageOrder(@RequestBody TradeOrderQueryPageDTO tradeOrderQueryPageDTO);

    /**
     * 查询接口,返回单个
     */
    @RequestMapping(value = "/order/queryOneOrder",method = RequestMethod.POST)
    public R<TradeOrderVO> queryOneOrder(@RequestBody TradeOrderQueryOneDTO tradeOrderQueryOneDTO);

    /**
     * 查询接口,返回list,未实现
     */
    @RequestMapping(value = "/order/queryListOrder",method = RequestMethod.POST)
    public R<List<TradeOrderVO>> queryListOrder(@RequestBody TradeOrderQueryListDTO tradeOrderQueryListDTO);

    /**
     * 统计查询接口
     *
     */
    @RequestMapping(value = "/order/queryOrderStatistics",method = RequestMethod.POST)
    public R<TradeOrderStatisticsVO>  queryOrderStatistics(@RequestBody TradeOrderQueryStatisticDTO tradeOrderQueryStatisticDTO);


    /**
     * 审核接口
     */
    @RequestMapping(value = "/order/auditOrder",method = RequestMethod.POST)
    public R<Void> auditOrder(@RequestBody AuditTradeOrderDTO auditOrderDTO);

//    /**已更改为通过 mq 回调
//     * 支付回调接口,包括支付成功和退款
//     * @param callbackOrderAfterPaySuccessDTO
//     * @return
//     */
//    @RequestMapping(value = "/order/orderPaymentCallback",method = RequestMethod.POST)
//    R<Void> orderPayCallback(@RequestBody CallbackOrderAfterPaySuccessDTO callbackOrderAfterPaySuccessDTO);


    /**
     * 创建退款单接口
     * @param orderRefundDTO
     * @return
     */
    @RequestMapping(value = "/order/createOrderRefund",method = RequestMethod.POST)
    R<OrderRefundVO> createOrderRefund(@RequestBody OrderRefundDTO orderRefundDTO);




    /**
     * 审核接口
     */
    @RequestMapping(value = "/test")
    public String test();



    /**
     * 日志接口
     */
    @RequestMapping(value = "/operator/log/queryListOperatorLog")
    R<List<OrderOperationLogVO>> queryListOperatorLog(@RequestBody TradeOrderLogQueryPageDTO tradeOrderQueryPageDTO);
    /**
     * 日志接口
     */
    @RequestMapping(value = "/operator/log/queryPageOperatorLog")
    R<TableDataInfo<OrderOperationLogVO>> queryPageOperatorLog(@RequestBody TradeOrderLogQueryPageDTO tradeOrderQueryPageDTO);



}
