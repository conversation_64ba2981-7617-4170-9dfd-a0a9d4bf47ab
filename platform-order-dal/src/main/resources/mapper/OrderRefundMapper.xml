<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderRefundMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderRefund">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="order_refund_no" jdbcType="VARCHAR" property="orderRefundNo" />
    <result column="price_change_no" jdbcType="VARCHAR" property="priceChangeNo" />
    <result column="total_amount" jdbcType="INTEGER" property="totalAmount" />
    <result column="received_amount" jdbcType="INTEGER" property="receivedAmount" />
    <result column="refund_status" jdbcType="VARCHAR" property="refundStatus" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>