package com.yuanshuo.platform.order.flow;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowFact;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;
import com.yuanshuo.common.entity.exception.BusinessException;
import com.yuanshuo.platform.order.api.enums.*;
import com.yuanshuo.platform.order.api.model.dto.query.TradeOrderQueryOneDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.api.model.vo.OrderStatusHistoryVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.flow.context.PayPaidContext;
import com.yuanshuo.platform.order.flow.context.order.CreateOrderContext;
import com.yuanshuo.platform.order.flow.context.order.CreateOrderRefundContext;
import com.yuanshuo.platform.order.flow.context.order.cmpData.*;
import com.yuanshuo.platform.order.mapper.po.OrderExtends;
import com.yuanshuo.platform.order.mapper.po.OrderRefund;
import com.yuanshuo.platform.order.mapper.po.TradeOrder;
import com.yuanshuo.platform.order.server.OrderRefundService;
import com.yuanshuo.platform.order.server.TradeOrderService;
import com.yuanshuo.platform.order.util.MqUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@LiteflowComponent
@Slf4j
public class TradeFlowConfig {
    @Autowired
    TradeOrderService tradeOrderService;
    @Autowired
    OrderRefundService orderRefundService;
    @Autowired
    MqUtil mqUtil;

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "generateOrderNoForYmdhs5num", nodeName = "生成订单编号(yyyymmddhhmmss+随机5位数字)", nodeType = NodeTypeEnum.COMMON)
    public void generateOrderNoForYmdhs5n(NodeComponent bindCmp, @LiteflowFact("createOrderContext") CreateOrderContext createOrderContext) {
        String requestData = bindCmp.getRequestData();
        createOrderContext.setGenerateOrderNo(() -> DateUtil.format(new Date(), "yyyyMMddHHmmss")
                + RandomUtil.randomNumbers(5));
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "generateItemOrderNoForYmdhs5num", nodeName = "生成订单编号(yyyymmddhhmmss+随机5位数字)", nodeType = NodeTypeEnum.COMMON)
    public void generateItemOrderNoForYmdhs5num(NodeComponent bindCmp, @LiteflowFact("createOrderContext") CreateOrderContext createOrderContext) {
        String requestData = bindCmp.getRequestData();
        createOrderContext.setGenerateItemOrderNo(() -> DateUtil.format(new Date(), "yyyyMMddHHmmss")
                + RandomUtil.randomNumbers(5));
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "generateChildOrderNoForYmdhs5num", nodeName = "生成订单编号(yyyymmddhhmmss+随机5位数字)", nodeType = NodeTypeEnum.COMMON)
    public void generateChildOrderNoForYmdhs5num(NodeComponent bindCmp, @LiteflowFact("createOrderContext") CreateOrderContext createOrderContext) {
        String requestData = bindCmp.getRequestData();
        createOrderContext.setChildGenerateOrderNo(() -> DateUtil.format(new Date(), "yyyyMMddHHmmss")
                + RandomUtil.randomNumbers(5));
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "insertOrder", nodeName = "创建订单", nodeType = NodeTypeEnum.COMMON)
    public void insertOrder(NodeComponent bindCmp, @LiteflowFact("createOrderContext") CreateOrderContext createOrderContext) {
        createOrderContext.setNodeId(bindCmp.getNodeId());
        createOrderContext.setNodeName(bindCmp.getName());
        TradeOrderVO order = tradeOrderService.createOrder(createOrderContext);
        createOrderContext.setTradeOrderVO(order);
    }


//    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_BOOLEAN, nodeId = "iFStatus", nodeName = "判断是否正常状态节点", nodeType = NodeTypeEnum.BOOLEAN)
//    public Boolean iFStatus(NodeComponent bindCmp) {
//        List<CmpSwitchStatusToProcessParam> cmpDataList = bindCmp.getBindDataList("switchStatus", CmpSwitchStatusToProcessParam.class);
//        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
//        baseContext.setNodeId(bindCmp.getNodeId());
//        baseContext.setNodeName(bindCmp.getName());
//        String tradeOrderNo = baseContext.getTradeOrderNo();
//        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
//        String status = cmpDataList.get(0).getStatus();
//        if (tradeOrder.getStatus().equals(status)) {
//            return true;
//        }
//        return false;
//    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_SWITCH, nodeId = "switchStatusToProcess", nodeName = "根据status判断进入哪个节点", nodeType = NodeTypeEnum.SWITCH)
    public String switchStatusToProcess(NodeComponent bindCmp) {
        List<CmpSwitchStatusToProcessParam> cmpDataList = bindCmp.getBindDataList("switchStatus", CmpSwitchStatusToProcessParam.class);
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        baseContext.setNodeId(bindCmp.getNodeId());
        baseContext.setNodeName(bindCmp.getName());
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        for (CmpSwitchStatusToProcessParam cmpSwitchStatusToProcessParam : cmpDataList) {
            if (tradeOrder.getStatus().equals(cmpSwitchStatusToProcessParam.getStatus())) {
                log.info("{}进入{}", tradeOrderNo, cmpSwitchStatusToProcessParam.getProcess());
                System.out.println(tradeOrderNo + "进入" + cmpSwitchStatusToProcessParam.getProcess());
                return cmpSwitchStatusToProcessParam.getProcess();
            }
        }
        log.error("{}异常状态,预计状态{},当前状态{}",tradeOrderNo, JSONUtil.toJsonStr(cmpDataList),tradeOrder.getStatus());
        throw new BusinessException(tradeOrderNo + "异常状态");
    }
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_SWITCH, nodeId = "switchPaymentStatusToProcess", nodeName = "根据paymentStatus判断进入哪个节点", nodeType = NodeTypeEnum.SWITCH)
    public String switchPaymentStatusToProcess(NodeComponent bindCmp) {
        List<CmpSwitchPaymentStatusToProcessParam> cmpDataList = bindCmp.getBindDataList("switchPaymentStatus", CmpSwitchPaymentStatusToProcessParam.class);
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        baseContext.setNodeId(bindCmp.getNodeId());
        baseContext.setNodeName(bindCmp.getName());
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);

        for (CmpSwitchPaymentStatusToProcessParam cmpSwitchPaymentStatusToProcessParam : cmpDataList) {
            if(Objects.isNull(tradeOrder.getPaymentStatus())){
                if(Objects.isNull(cmpSwitchPaymentStatusToProcessParam.getPaymentStatus())){
                    return cmpSwitchPaymentStatusToProcessParam.getProcess();
                }
            }else if (tradeOrder.getPaymentStatus().equals(cmpSwitchPaymentStatusToProcessParam.getPaymentStatus())) {
                log.info("{}进入{}", tradeOrderNo, cmpSwitchPaymentStatusToProcessParam.getProcess());
                System.out.println(tradeOrderNo + "进入" + cmpSwitchPaymentStatusToProcessParam.getProcess());
                return cmpSwitchPaymentStatusToProcessParam.getProcess();
            }
        }
        throw new BusinessException(tradeOrderNo + "异常支付状态");
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_SWITCH, nodeId = "switchFulfillmentStatusToProcess", nodeName = "根据fulfillmentStatus判断进入哪个节点", nodeType = NodeTypeEnum.SWITCH)
    public String switchFulfillmentStatusToProcess(NodeComponent bindCmp) {
        List<CmpSwitchFulfillmentStatusToProcessParam> cmpDataList = bindCmp.getBindDataList("switchFulfillmentStatus", CmpSwitchFulfillmentStatusToProcessParam.class);
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        baseContext.setNodeId(bindCmp.getNodeId());
        baseContext.setNodeName(bindCmp.getName());
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);

        for (CmpSwitchFulfillmentStatusToProcessParam cmpSwitchFulfillmentStatusToProcessParam : cmpDataList) {
            if(Objects.isNull(tradeOrder.getFulfillmentStatus())){
                if(Objects.isNull(cmpSwitchFulfillmentStatusToProcessParam.getFulfillmentStatus())){
                    return cmpSwitchFulfillmentStatusToProcessParam.getProcess();
                }
            }else if (tradeOrder.getFulfillmentStatus().equals(cmpSwitchFulfillmentStatusToProcessParam.getFulfillmentStatus())) {
                log.info("{}进入{}", tradeOrderNo, cmpSwitchFulfillmentStatusToProcessParam.getProcess());
                System.out.println(tradeOrderNo + "进入" + cmpSwitchFulfillmentStatusToProcessParam.getProcess());
                return cmpSwitchFulfillmentStatusToProcessParam.getProcess();
            }
        }
        throw new BusinessException(tradeOrderNo + "异常履约状态");
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_SWITCH, nodeId = "switchRefundStatusToProcess", nodeName = "根据RefundStatus判断进入哪个节点", nodeType = NodeTypeEnum.SWITCH)
    public String switchRefundStatusToProcess(NodeComponent bindCmp) {
        List<CmpSwitchRefundStatusToProcessParam> cmpDataList = bindCmp.getBindDataList("switchRefundStatus", CmpSwitchRefundStatusToProcessParam.class);
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        baseContext.setNodeId(bindCmp.getNodeId());
        baseContext.setNodeName(bindCmp.getName());
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);

        for (CmpSwitchRefundStatusToProcessParam cmpSwitchRefundStatusToProcessParam : cmpDataList) {
            if (tradeOrder.getRefundStatus().equals(cmpSwitchRefundStatusToProcessParam.getRefundStatus())) {
                log.info("{}进入{}", tradeOrderNo, cmpSwitchRefundStatusToProcessParam.getProcess());
                System.out.println(tradeOrderNo + "进入" + cmpSwitchRefundStatusToProcessParam.getProcess());
                return cmpSwitchRefundStatusToProcessParam.getProcess();
            }
        }
        throw new BusinessException(tradeOrderNo + "异常退款状态");
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_SWITCH, nodeId = "switchRefundOrderStatusToProcess", nodeName = "根据refundOrderStatus判断进入哪个节点", nodeType = NodeTypeEnum.SWITCH)
    public String switchRefundOrderStatusToProcess(NodeComponent bindCmp) {
        List<CmpSwitchRefundOrderStatusToProcessParam> cmpDataList = bindCmp.getBindDataList("switchRefundOrderStatus", CmpSwitchRefundOrderStatusToProcessParam.class);
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        baseContext.setNodeId(bindCmp.getNodeId());
        baseContext.setNodeName(bindCmp.getName());
        String orderRefundNo = baseContext.getOrderRefundNo();
        OrderRefund orderRefund = orderRefundService.queryByOrderRefundNoMust(orderRefundNo);

        for (CmpSwitchRefundOrderStatusToProcessParam cmpSwitchRefundOrderStatusToProcessParam : cmpDataList) {
            if (orderRefund.getRefundStatus().equals(cmpSwitchRefundOrderStatusToProcessParam.getRefundOrderStatus())) {
                log.info("{}进入{}", orderRefundNo, cmpSwitchRefundOrderStatusToProcessParam.getProcess());
                System.out.println(orderRefundNo + "进入" + cmpSwitchRefundOrderStatusToProcessParam.getProcess());
                return cmpSwitchRefundOrderStatusToProcessParam.getProcess();
            }
        }
        throw new BusinessException(orderRefundNo + "异常退款状态");
    }


    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "insertOrderOperationLog", nodeName = "插入order_operation_log表", nodeType = NodeTypeEnum.COMMON)
    public void insertOrderOperationLog(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        baseContext.setNodeId(bindCmp.getNodeId());
        baseContext.setNodeName(bindCmp.getName());
        String tradeOrderNo = baseContext.getTradeOrderNo();
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(tradeOrderNo);
        tradeOrderService.insertOrderOperationLog(tradeOrder,baseContext);
    }


    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdateOrderExtend", nodeName = "更新订单扩展表的ext_data数据", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdateOrderExtend(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        baseContext.setNodeId(bindCmp.getNodeId());
        baseContext.setNodeName(bindCmp.getName());

        String tradeOrderNo = baseContext.getTradeOrderNo();
        OrderExtends orderExtends = tradeOrderService.queryOrderExtendsByOrderNo(tradeOrderNo);
        if (Objects.isNull(orderExtends)) {
            log.info("{}订单扩展表不存在", tradeOrderNo);
            return;
        }
        String extData = orderExtends.getExtData();

        JSONObject extDataJsonObject = JSONObject.parseObject(extData);

        JSONObject jsonObject = baseContext.getJsonObject();

        OrderExtends updateOrderExtends = new OrderExtends();
        updateOrderExtends.setId(orderExtends.getId());
        mergeUsingJsonPath(extDataJsonObject, jsonObject);

        updateOrderExtends.setExtData(extDataJsonObject.toJSONString());

        tradeOrderService.updateOrderExtends(updateOrderExtends);


    }

    public static void mergeUsingJsonPath(JSONObject target, JSONObject source) {
        // 遍历source的所有键值对
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String path = entry.getKey();
            Object value = entry.getValue();

            // 1. 拆分路径（如 "user.name.first" -> ["user", "name", "first"]）
            String[] pathSegments = path.split("\\.");
            StringBuilder currentPath = new StringBuilder("$");

            // 2. 逐级检查父路径
            for (int i = 0; i < pathSegments.length - 1; i++) {
                currentPath.append(".").append(pathSegments[i]);
                Object parentValue = JSONPath.eval(target, currentPath.toString());

                // 如果父路径是基本类型或不存在，则初始化为空 Map
                if (parentValue == null || isBasicType(parentValue)) {
                    JSONPath.set(target, currentPath.toString(), new HashMap<>());
                }
            }
            // 3. 设置最终值
            try {
                JSONPath.set(target, "$." + path, value);
            } catch (Exception e) {
                System.err.println("Failed to set path: " + path + ", error: " + e.getMessage());
            }
        }
    }

    /** 判断是否为基本类型（String/Number/Boolean） */
    private static boolean isBasicType(Object obj) {
        return obj instanceof String || obj instanceof Number || obj instanceof Boolean;
    }



    //有金额
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdateOrderToPaid", nodeName = "更新订单状态&支付状态到已支付", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdateOrderToPaid(NodeComponent bindCmp, @LiteflowFact("payPaidContext") PayPaidContext payPaidContext) {
        payPaidContext.setNodeId(bindCmp.getNodeId());
        payPaidContext.setNodeName(bindCmp.getName());
        tradeOrderService.updateOrderToPaid(payPaidContext);
    }






    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeCreateRefundOrder", nodeName = "创建退款单", nodeType = NodeTypeEnum.COMMON)
    public void nodeCreateRefundOrder(NodeComponent bindCmp, @LiteflowFact("createOrderRefundContext") CreateOrderRefundContext createOrderRefundContext) {
        createOrderRefundContext.setNodeId(bindCmp.getNodeId());
        createOrderRefundContext.setNodeName(bindCmp.getName());
        OrderRefundVO orderRefundVO = orderRefundService.createOrderRefund(createOrderRefundContext);
        createOrderRefundContext.setOrderRefundVO(orderRefundVO);
    }




    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdateOrderStatusTo", nodeName = "更新订单状态为", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdateOrderStatusTo(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        String status = bindCmp.getBindData("status", String.class);
        log.info("nodeUpdateOrderStatusTo:{}更新订单总状态为{}",baseContext.getTradeOrderNo(),status);
        EnumTradeOrderStatus enumStatus = EnumTradeOrderStatus.getByCode(status);
        if (enumStatus==null) {
            throw new BusinessException("flow状态入参错误");
        }
        baseContext.setNodeId(bindCmp.getNodeId()+enumStatus.getCode());
        baseContext.setNodeName(bindCmp.getName() + enumStatus.getMsg());
        tradeOrderService.updateOrderStatusTo(baseContext, enumStatus);
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdatePaymentStatusTo", nodeName = "更新订单支付状态为", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdatePaymentStatusTo(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        String status = bindCmp.getBindData("status", String.class);
        log.info("chainId:{},nodeUpdatePaymentStatusTo:{}更新订单支付状态为{}",bindCmp.getChainId(),baseContext.getTradeOrderNo(),status);
        EnumOrderPaymentStatus statusByCode = EnumOrderPaymentStatus.getByCode(status);
        if (statusByCode==null) {
            throw new BusinessException("flow状态入参错误");
        }
        baseContext.setNodeId(bindCmp.getNodeId()+ statusByCode.getCode());
        baseContext.setNodeName(bindCmp.getName() + statusByCode.getMsg());
        tradeOrderService.updatePaymentStatusTo(baseContext, statusByCode);
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdateOrderRefundStatusTo", nodeName = "更新订单退款状态为", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdateOrderRefundStatusTo(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        String status = bindCmp.getBindData("status", String.class);
        log.info("nodeUpdateOrderRefundStatusTo:{}更新订单退款状态为{}",baseContext.getTradeOrderNo(),status);
        EnumOrderRefundStatus statusByCode = EnumOrderRefundStatus.getByCode(status);
        if (statusByCode==null) {
            throw new BusinessException("flow状态入参错误");
        }
        baseContext.setNodeId(bindCmp.getNodeId()+ statusByCode.getCode());
        baseContext.setNodeName(bindCmp.getName() + statusByCode.getMsg());
        tradeOrderService.updateOrderRefundStatusTo(baseContext, statusByCode);
    }
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdateOrderFulfillmentStatusTo", nodeName = "更新订单履约状态为", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdateOrderFulfillmentStatusTo(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        String status = bindCmp.getBindData("fulfillmentStatus", String.class);
        log.info("nodeUpdateOrderFulfillmentStatusTo:{}更新订单履约状态为{}",baseContext.getTradeOrderNo(),status);
        EnumFulfillmentStatus statusByCode = EnumFulfillmentStatus.getByCode(status);
        if (statusByCode==null) {
            throw new BusinessException("flow状态入参错误");
        }
        baseContext.setNodeId(bindCmp.getNodeId()+ statusByCode.getCode());
        baseContext.setNodeName(bindCmp.getName() + statusByCode.getMsg());
        tradeOrderService.updateOrderFulfillmentStatusTo(baseContext,statusByCode);
    }



    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeUpdateRefundOrderStatusTo", nodeName = "更新退款单状态为", nodeType = NodeTypeEnum.COMMON)
    public void nodeUpdateRefundOrderStatusTo(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        String status = bindCmp.getBindData("status", String.class);
        log.info("nodeUpdateRefundOrderStatusTo:{}更新退款单状态为{}",baseContext.getTradeOrderNo(),status);
        EnumOrderRefundStatus statusByCode = EnumOrderRefundStatus.getByCode(status);
        if (statusByCode==null) {
            throw new BusinessException("flow状态入参错误");
        }
        baseContext.setNodeId(bindCmp.getNodeId()+ statusByCode.getCode());
        baseContext.setNodeName(bindCmp.getName() + statusByCode.getMsg());
        orderRefundService.updateRefundOrderStatusTo(baseContext, statusByCode);
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "orderStatusRollback", nodeName = "订单主状态回退", nodeType = NodeTypeEnum.COMMON)
    public void orderStatusRollback(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        String status = bindCmp.getBindData("status", String.class);

        log.info("orderStatusRollback:{}状态回退到{}",baseContext.getTradeOrderNo(),status);
        String tradeOrderNo = baseContext.getTradeOrderNo();

        if(StrUtil.isNotBlank(status)){
            EnumTradeOrderStatus statusByCode = EnumTradeOrderStatus.getByCode(status);
            if (statusByCode==null) {
                throw new BusinessException("flow状态入参错误");
            }
        }else{
            TradeOrderQueryOneDTO tradeOrderQueryOneDTO = new TradeOrderQueryOneDTO();
            tradeOrderQueryOneDTO.setTradeOrderNo(tradeOrderNo);
            TradeOrderVO tradeOrderVO = tradeOrderService.queryOneOrder(tradeOrderQueryOneDTO);
            status = tradeOrderVO.getStatus();
        }
        EnumTradeOrderStatus enumStatus = EnumTradeOrderStatus.getByCode(status);
        if (enumStatus==null) {
            throw new BusinessException("flow状态入参错误");
        }
        baseContext.setNodeId(bindCmp.getNodeId()+enumStatus.getCode());
        baseContext.setNodeName(bindCmp.getName() + enumStatus.getMsg());
        OrderStatusHistoryVO orderStatusHistoryVO =  tradeOrderService.queryOrderStatusHistory(tradeOrderNo, enumStatus);
        if (orderStatusHistoryVO==null) {
            log.info("orderStatusRollback:{}没有历史数据",tradeOrderNo);
            return;
        }
        String beforeStatus = orderStatusHistoryVO.getBeforeStatus();
        EnumTradeOrderStatus byCode = EnumTradeOrderStatus.getByCode(beforeStatus);
        if (byCode==null) {
            throw new BusinessException("回退状态有误");
        }
        tradeOrderService.updateOrderStatusTo(baseContext, byCode);
    }



    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeSendMqForTradeVo", nodeName = "发送mq节点", nodeType = NodeTypeEnum.COMMON)
    public void nodeSendMqForTradeVo(NodeComponent bindCmp) {
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        CmpMqParam cmpMqParam = bindCmp.getBindData("mqData",CmpMqParam.class);
        String tag = cmpMqParam.getTag();

        EnumOrderMqSendTag tagByCode = EnumOrderMqSendTag.getByCode(tag);
        if (tagByCode == null) {
            throw new BusinessException("mq tag错误"+tag);
        }
        TradeOrderQueryOneDTO tradeOrderQueryOneDTO = new TradeOrderQueryOneDTO();
        String tradeOrderNo = baseContext.getTradeOrderNo();
        tradeOrderQueryOneDTO.setTradeOrderNo(tradeOrderNo);
        TradeOrderVO tradeOrderVO = tradeOrderService.queryOneOrder(tradeOrderQueryOneDTO);
        mqUtil.sendPlatformOrderMq(tag, tradeOrderVO, tradeOrderNo);

    }


    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeWarnProcess", nodeName = "告警节点", nodeType = NodeTypeEnum.COMMON)
    public void nodeWarnProcess(NodeComponent bindCmp) {
        String  msg = bindCmp.getBindData("msg",String.class);
        BaseContext baseContext = bindCmp.getContextBean(BaseContext.class);
        log.warn("nodeWarn,context:{},msg:{}", baseContext, msg);
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "nodeEmptyProcess", nodeName = "空节点", nodeType = NodeTypeEnum.COMMON)
    public void nodeEmptyProcess(NodeComponent bindCmp) {
        log.info("nodeEmptyProcess");
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "parentUpdateSyncChild", nodeName = "父订单更新状态同步子订单状态", nodeType = NodeTypeEnum.COMMON)
    public void parentUpdateChild(NodeComponent bindCmp) {
        // 1. 获取上下文
        BaseContext context = bindCmp.getContextBean(BaseContext.class);
        String currentOrderNo = context.getTradeOrderNo();

        // 2. 判断是否是父订单（通过服务层查询）
        TradeOrder currentOrder = tradeOrderService.queryByOrderNoMust(currentOrderNo);
        if (Objects.isNull(currentOrder.getParentType()) || !currentOrder.getParentType()) {
            log.debug("订单[{}]不是父订单，跳过子订单同步", currentOrderNo);
            return;
        }

        // 3. 获取子订单列表
        List<String> childOrderNos = tradeOrderService.getChildOrderNos(currentOrderNo);
        if (CollectionUtils.isEmpty(childOrderNos)) {
            log.debug("父订单[{}]没有子订单，跳过同步", currentOrderNo);
            return;
        }

        // 4. 获取要同步的状态
        String status = currentOrder.getStatus();
        String paymentStatus = currentOrder.getPaymentStatus();
        String refundStatus = currentOrder.getRefundStatus();
        //todo 2025-06-15 履约状态
        childOrderNos.forEach(childOrderNo -> {
            try {
                BaseContext childContext = new BaseContext();
                childContext.setTradeOrderNo(childOrderNo);
                childContext.setNodeId(bindCmp.getNodeId());
                childContext.setNodeName(bindCmp.getName());
                childContext.setChainId(context.getChainId());
                childContext.setChainName(context.getChainName());
                childContext.setOperatorUserId(context.getOperatorUserId());
                childContext.setOperatorUserName(context.getOperatorUserName());

                tradeOrderService.syncStatus(childContext,status,paymentStatus,refundStatus);
            } catch (Exception e) {
                log.error("同步子订单[{}]状态失败: {}", childOrderNo, e.getMessage());
            }
        });
    }
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "childUpdateSyncParent", nodeName = "子订单更新同步父订单状态", nodeType = NodeTypeEnum.COMMON)
    public void childUpdateSyncParent(NodeComponent bindCmp) {
        // 1. 获取上下文
        BaseContext context = bindCmp.getContextBean(BaseContext.class);
        String currentOrderNo = context.getTradeOrderNo();

        // 2. 判断是否是子订单
        TradeOrder currentOrder = tradeOrderService.queryByOrderNoMust(currentOrderNo);
        if (Objects.isNull(currentOrder.getParentType()) || currentOrder.getParentType()) {
            log.debug("订单[{}]不是子订单，跳过父订单同步", currentOrderNo);
            return;
        }
        // 3. 获取父订单号
        String parentOrderNo = currentOrder.getParentNo();
        // 4. 获取要同步的状态
        String status = currentOrder.getStatus();
        String paymentStatus = currentOrder.getPaymentStatus();
        String refundStatus = currentOrder.getRefundStatus();

        try {
            BaseContext parentContext = new BaseContext();
            parentContext.setTradeOrderNo(parentOrderNo);
            parentContext.setNodeId(bindCmp.getNodeId());
            parentContext.setNodeName(bindCmp.getName());
            parentContext.setChainId(context.getChainId());
            parentContext.setChainName(context.getChainName());
            parentContext.setOperatorUserId(context.getOperatorUserId());
            parentContext.setOperatorUserName(context.getOperatorUserName());
            tradeOrderService.syncStatus(parentContext,status,paymentStatus,refundStatus);

        } catch (Exception e) {
            throw new BusinessException("同步父订单状态失败: " + e.getMessage());
        }
    }
}
