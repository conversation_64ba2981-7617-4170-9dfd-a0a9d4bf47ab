<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderPriceChangeMapper">

    <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderPriceChange">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo"/>
        <result column="change_type" jdbcType="VARCHAR" property="changeType"/>
        <result column="payment_status" jdbcType="VARCHAR" property="paymentStatus"/>
        <result column="before_total_amount" jdbcType="INTEGER" property="beforeTotalAmount"/>
        <result column="before_total_discount" jdbcType="INTEGER" property="beforeTotalDiscount"/>
        <result column="before_total_payable" jdbcType="INTEGER" property="beforeTotalPayable"/>
        <result column="after_total_amount" jdbcType="INTEGER" property="afterTotalAmount"/>
        <result column="after_total_discount" jdbcType="INTEGER" property="afterTotalDiscount"/>
        <result column="after_total_paid" jdbcType="INTEGER" property="afterTotalPaid"/>
        <result column="after_total_payable" jdbcType="INTEGER" property="afterTotalPayable"/>
        <result column="amount_difference" jdbcType="INTEGER" property="amountDifference"/>
        <result column="fund_process_type" jdbcType="VARCHAR" property="fundProcessType"/>
        <result column="fund_process_amount" jdbcType="INTEGER" property="fundProcessAmount"/>
        <result column="fund_process_status" jdbcType="VARCHAR" property="fundProcessStatus"/>
        <result column="change_status" jdbcType="VARCHAR" property="changeStatus"/>
        <result column="approval_status" jdbcType="VARCHAR" property="approvalStatus"/>
        <result column="approval_time" jdbcType="TIMESTAMP" property="approvalTime"/>
        <result column="change_reason" jdbcType="VARCHAR" property="changeReason"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <select id="selectByTradeOrderNo" resultMap="BaseResultMap">
        SELECT * FROM order_price_change WHERE trade_order_no = #{tradeOrderNo} ORDER BY create_time DESC
    </select>

    <select id="selectLatestByTradeOrderNo" resultMap="BaseResultMap">
        SELECT * FROM order_price_change WHERE trade_order_no = #{tradeOrderNo} ORDER BY create_time DESC LIMIT 1
    </select>

    <select id="selectByTradeOrderNoAndFundProcessStatusIn" resultMap="BaseResultMap">
            SELECT * FROM order_price_change
            WHERE trade_order_no = #{tradeOrderNo}
            AND fund_process_status IN
            <foreach item='item' index='index' collection='fundProcessStatuses' open='(' separator=',' close=')'>
                #{item}
            </foreach>
            ORDER BY create_time DESC
    </select>
    
    <select id="selectByPriceChangeNo" resultMap="BaseResultMap">
        SELECT * FROM order_price_change WHERE price_change_no = #{priceChangeNo} AND is_delete = 0
    </select>
</mapper>