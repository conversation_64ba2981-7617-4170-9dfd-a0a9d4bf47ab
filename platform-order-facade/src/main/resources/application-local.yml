server:
  port: 8082
spring:
  datasource:
    url: *****************************************************************************************************************************************************************
    username: user-dev
    password: dasdwqce25bv3vr3
    driver-class-name: com.mysql.cj.jdbc.Driver
  data:
    redis:
      host: r-bp1gljqexkio4c3eh2.redis.rds.aliyuncs.com
      port: 6379
      password:
  cloud:
    nacos:
      username: nacos
      password: nacos123
      config:
        server-addr:  nacos-headless.yszx.ink:8848
        file-extension: yml
        namespace: dev
        enabled: false
      discovery:
        server-addr: nacos-headless.yszx.ink:8848
        namespace:  dev
        group: local
        ip-type: IPv4
logging:
  config: classpath:custom-logback.xml


