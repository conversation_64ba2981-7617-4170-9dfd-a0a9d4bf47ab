package com.yuanshuo.platform.order.mapper.po;

import com.yuanshuo.platform.order.mapper.enums.FlowEventType;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * 表名：order_flow_step
 * 订单流程步骤表（行转列后的新表）
 */
@Data
@Entity
@Table(name = "order_flow_step")
public class OrderFlowStep {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 订单类型 (源自 order_flow.order_type)
     */
    @Column(name = "order_type")
    private String orderType;

    /**
     * 流程事件类型 (源自 order_flow 表的列名, e.g., create_order)
     */
    @Column(name = "flow_event_type")
    @Enumerated(EnumType.STRING) // 持久化枚举的名称或字符串表示
    private FlowEventType flowEventType;

    /**
     * 链ID (原chain_name)
     */
    @Column(name = "chain_id")
    private String chainId;

    /**
     * 链名称 (原description)
     */
    @Column(name = "chain_name")
    private String chainName;

    /**
     * 描述信息 (与chain_name内容保持一致)
     */
    @Column(name = "description")
    private String description;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}