<flow>

    <!-- app创建订单 -->
    <chain name="createOrderForAppPlaceOrder">
        THEN(
            PAR(
                generateOrderNoForYmdhs5num,
                generateItemOrderNoForYmdhs5num,
                generateChildOrderNoForYmdhs5num
            ),
            insertOrder
        );
    </chain>

    <!-- 销售小工具创建订单 -->
    <chain name="createOrderForTradeLittleSeller">
        THEN(
            PAR(
                generateOrderNoForYmdhs5num,
                generateItemOrderNoForYmdhs5num
            ),
            insertOrder
        );
    </chain>

    <!-- app创建退款单流程 -->
    <chain name="createRefundOrderForAppPlaceOrder">
        cmpData = '[{"status":"paying","process":"nodeCreateRefundOrder"},{"status":"completed","process":"nodeCreateRefundOrder"},{"status":"fulfilling","process":"nodeCreateRefundOrder"}]';
            THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            ).to(
                nodeCreateRefundOrder
            ),
            nodeUpdateOrderStatusToRefunding,
            nodeUpdateOrderRefundStatusToRefundInitialize,
            syncStatus
        );
    </chain>

    <!-- 销售小工具创建退款单流程 -->
    <chain name="createRefundOrderForTradeLittleSeller">
        cmpData = '[{"status":"completed","process":"nodeCreateRefundOrder"},{"status":"fulfilling","process":"nodeCreateRefundOrder"}]';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            ).to(
                nodeCreateRefundOrder
            ),
            nodeUpdateOrderStatusToRefunding,
            nodeUpdateOrderRefundStatusToRefundInitialize
        );
    </chain>

<!--    app创建改价单流程-->
    <!-- app创建订单 -->
    <chain name="createPriceChangeOrderForApp">
        mqDataSeller = '{"tag":"order_price_change_success"}';
        THEN(
        createPriceChange,
        nodeSendMqForTradeVo.bind("mqData",mqDataSeller)
        );
    </chain>

</flow>