package com.yuanshuo.platform.order.api.enums;

public enum EnumOrderPaymentStatus {
    UNPAID("unpaid", "用户下单成功，未创建支付单"),
    PENDING_PAYMENT("pending_payment","创建支付单成功，等待用户支付"),
    PAID("paid", "支付渠道返回支付成功结果，资金已到账"),
    FAILED("failed", "支付渠道返回支付失败结果"),
    CANCELLING("cancelling", "用户取消订单"),
    CANCELLED("cancelled", "支付失败取消");

    private String code;
    private String msg;

    EnumOrderPaymentStatus(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
    //根据code获取enum
    public static EnumOrderPaymentStatus getByCode(String code) {
        for (EnumOrderPaymentStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
