package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * 订单改价记录VO
 */
@Data
public class OrderPriceChangeVO  {
    /**
     * 主键ID（自增）
     */
    private Long id;

    /**
     * 订单号
     */
    private String tradeOrderNo;

    /**
     * 改价单号
     */
    private String priceChangeNo;

    /**
     * 改价类型：before_payment-支付前改价，after_payment-支付后改价
     */
    private String changeType;

    /**
     * 改价时支付状态
     */
    private String paymentStatus;

    /**
     * 改价前总金额（单位：分）
     */
    private Integer beforeTotalAmount;

    /**
     * 改价前总优惠金额（单位：分）
     */
    private Integer beforeTotalDiscount;

    /**
     * 改价前总应付金额（单位：分）
     */
    private Integer beforeTotalPayable;

    /**
     * 改价后总金额（单位：分）
     */
    private Integer afterTotalAmount;

    /**
     * 改价后总优惠金额（单位：分）
     */
    private Integer afterTotalDiscount;

    /**
     * 改价后总应付金额（单位：分）
     */
    private Integer afterTotalPayable;

    /**
     * 金额差额（单位：分，正数为涨价，负数为降价）
     */
    private Integer amountDifference;

    /**
     * 改价原因
     */
    private String changeReason;

    /**
     * 改价状态：approved-已通过（自动审核）
     */
    private String changeStatus;

    /**
     * 审核状态：auto_approved-自动通过，manual_approved-人工通过，rejected-拒绝
     */
    private String approvalStatus;

    /**
     * 审核人ID
     */
    private Long approverId;

    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 审核时间
     */
    private Date approvalTime;

    /**
     * 审核备注
     */
    private String approvalRemark;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 资金处理状态：pending-待处理，processing-处理中，completed-已完成，failed-失败
     */
    private String fundProcessStatus;

    /**
     * 资金处理类型：none-无需处理，refund-退款，supplement-补款
     */
    private String fundProcessType;

    /**
     * 资金处理金额（单位：分）
     */
    private Integer fundProcessAmount;

    /**
     * 资金处理单号（退款单号或补款单号）
     */
    private String fundProcessNo;

    /**
     * 资金处理完成时间
     */
    private Date fundProcessTime;

    /**
     * 资金处理备注
     */
    private String fundProcessRemark;
}