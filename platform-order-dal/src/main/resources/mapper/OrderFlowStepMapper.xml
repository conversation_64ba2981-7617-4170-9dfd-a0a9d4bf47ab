<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderFlowStepMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderFlowStep">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="flow_event_type" jdbcType="VARCHAR" property="flowEventType" />
    <result column="chain_id" property="chainId" jdbcType="VARCHAR"/>
    <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <!-- 如果有自定义查询，可以在这里添加 -->

</mapper>