package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * 表名：administrative_division
 * 表注释：行政区划表
 */
@Data
@Table(name = "administrative_division")
public class AdministrativeDivision {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 国标编码
     */
    @Column(name = "code")
    private String code;

    /**
     * 行政区名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 经度
     */
    @Column(name = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @Column(name = "latitude")
    private Double latitude;

    /**
     * 上级行政区ID
     */
    @Column(name = "parent_code")
    private String parentCode;

    /**
     * 级别：province=省 city=市 district=区/县 street=镇
     */
    @Column(name = "level")
    private String level;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 删除标识 0未删除 1已删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;
}