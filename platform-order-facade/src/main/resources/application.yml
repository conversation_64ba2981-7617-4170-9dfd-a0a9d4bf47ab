spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  config:
    import:
      - optional:application-${spring.profiles.active}.yml
      - optional:nacos:${spring.application.name}.${spring.cloud.nacos.config.file-extension}
      - optional:nacos:datasource-mysql.yml
      - optional:nacos:datasource-redis.yml
  application:
    name: platform-order
  datasource:
    url: ${mysql.${spring.application.name}.url}
    username: ${mysql.${spring.application.name}.username}
    password: ${mysql.${spring.application.name}.password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  data:
    redis:
      host: ${redis.${spring.application.name}.host}
      port: ${redis.${spring.application.name}.port}
      password: ${redis.${spring.application.name}.password}
  task:
    scheduling:
      pool:
        size: 10
  cloud:
    nacos:
      username: nacos
      password: nacos123
      config:
        server-addr: ${NACOS_SERVER_ADDR}
        file-extension: yml
        namespace: ${NACOS_NAMESPACE}
        refreshEnabled: true
      discovery:
        server-addr: ${NACOS_SERVER_ADDR}
        namespace: ${NACOS_NAMESPACE}
        ip-type: IPv4
liteflow:
  rule-source: flow/*.xml

mybatis:
  mapper-locations: classpath*:mapper/*Mapper.xml
  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    mapUnderscoreToCamelCase: true

pagehelper:
  propertyName: propertyValue
  reasonable: false
  defaultCount: true

mapper:
  wrapKeyword: '`{0}`'

management:
  endpoints:
    web:
      exposure:
        include: prometheus,health,info,metrics
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
xxl:
  appName: ${spring.application.name}-${spring.profiles.active}
  accessToken: default_token
  adminAddresses: http://xxl.yszx.ink
jasypt:
  encryptor:
    password:
rocketmq:
  name-server: ************:9876
  producer:
    group: ${spring.application.name}-${spring.profiles.active}