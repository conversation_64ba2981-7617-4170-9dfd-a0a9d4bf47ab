package com.yuanshuo.platform.order.server.flow;

import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yuanshuo.platform.order.api.model.dto.BaseDTO;
import com.yuanshuo.platform.order.api.model.dto.CallbackAfterPaySuccessDTO;
import com.yuanshuo.platform.order.api.model.dto.CallbackAfterRefundSuccessDTO;
import com.yuanshuo.platform.order.api.model.dto.OrderPriceChangeDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderPriceChangeVO;
import com.yuanshuo.platform.order.common.BusinessException;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import com.yuanshuo.platform.order.flow.context.PayPaidContext;
import com.yuanshuo.platform.order.flow.context.PayRefundContext;
import com.yuanshuo.platform.order.flow.context.pricechange.CreatePriceChangeContext;
import com.yuanshuo.platform.order.mapper.OrderFlowMapper;
import com.yuanshuo.platform.order.mapper.OrderFlowStepMapper;
import com.yuanshuo.platform.order.mapper.enums.FlowEventType;
import com.yuanshuo.platform.order.mapper.po.OrderFlowStep;
import com.yuanshuo.platform.order.mapper.po.OrderPriceChange;
import com.yuanshuo.platform.order.mapper.po.OrderRefund;
import com.yuanshuo.platform.order.mapper.po.TradeOrder;
import com.yuanshuo.platform.order.server.OrderPriceChangeService;
import com.yuanshuo.platform.order.server.OrderRefundService;
import com.yuanshuo.platform.order.server.TradeOrderService;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class PriceChangeFlowService {
    @Resource
    private FlowExecutor flowExecutor;
    @Resource
    private OrderFlowMapper orderFlowMapper;
    @Resource
    private OrderFlowStepMapper orderFlowStepMapper;
    @Autowired
    private TradeOrderService tradeOrderService;
    @Autowired
    private OrderPriceChangeService orderPriceChangeService;
    @Autowired
    private OrderRefundService orderRefundService;

    @SneakyThrows
    @Transactional
    public OrderPriceChangeVO createOrder(OrderPriceChangeDTO orderPriceChangeDTO) {
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(orderPriceChangeDTO.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CREATE_PRICE_CHANGE_ORDER);

        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep( orderFlowStep,orderPriceChangeDTO);
        CreatePriceChangeContext createPriceChangeContext = new CreatePriceChangeContext(baseContext);
        createPriceChangeContext.setOrderPriceChangeDTO(orderPriceChangeDTO);


        LiteflowResponse response = flowExecutor.execute2Resp(createPriceChangeContext.getChainId(), null, createPriceChangeContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
        OrderPriceChangeVO orderPriceChangeVO = response.getContextBean(CreatePriceChangeContext.class).getOrderPriceChangeVO();
        return orderPriceChangeVO;
    }

    @SneakyThrows
    @Transactional
    public void callbackPriceChangeAfterPaySuccess(CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO) {
        OrderPriceChange orderPriceChange = orderPriceChangeService.getByPriceChangeNo(callbackAfterPaySuccessDTO.getBusinessNo());
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(orderPriceChange.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PRICE_CHANGE_ORDER_PAID_SUCCESS);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackAfterPaySuccessDTO);
        PayPaidContext payPaidContext = new PayPaidContext(baseContext);
        payPaidContext.setTradeOrderNo(tradeOrder.getTradeOrderNo());
        payPaidContext.setPriceChangeNo(callbackAfterPaySuccessDTO.getBusinessNo());
        payPaidContext.setAmount(callbackAfterPaySuccessDTO.getAmount());
        payPaidContext.setPayChannel(callbackAfterPaySuccessDTO.getPayChannel());
        payPaidContext.setPaymentReceiptNo(callbackAfterPaySuccessDTO.getPaymentReceiptNo());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, payPaidContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }

    @SneakyThrows
    @Transactional
    public void callbackPriceChangeAfterRefundSuccess(CallbackAfterRefundSuccessDTO callbackAfterRefundSuccessDTO) {
        OrderRefund orderRefund = orderRefundService.queryByOrderRefundNoMust(callbackAfterRefundSuccessDTO.getOrderRefundNo());

        OrderPriceChange orderPriceChange = orderPriceChangeService.getByPriceChangeNo(orderRefund.getPriceChangeNo());
        TradeOrder tradeOrder = tradeOrderService.queryByOrderNoMust(orderPriceChange.getTradeOrderNo());
        OrderFlowStep orderFlowStep = queryFlowStepByOrderTypeAndEventType(tradeOrder.getOrderType(), FlowEventType.CALLBACK_PRICE_CHANGE_ORDER_REFUND_SUCCESS);
        BaseContext baseContext = buildBaseContextChainIdAndChainNameFromStep(orderFlowStep, callbackAfterRefundSuccessDTO);
        PayRefundContext payRefundContext = new PayRefundContext(baseContext);
        payRefundContext.setTradeOrderNo(tradeOrder.getTradeOrderNo());
        payRefundContext.setPriceChangeNo(orderPriceChange.getPriceChangeNo());
        payRefundContext.setAmount(callbackAfterRefundSuccessDTO.getAmount());
        payRefundContext.setPayChannel(callbackAfterRefundSuccessDTO.getPayChannel());
        payRefundContext.setPaymentReceiptRefundNo(callbackAfterRefundSuccessDTO.getPaymentReceiptRefundNo());
        LiteflowResponse response = flowExecutor.execute2Resp(baseContext.getChainId(), null, payRefundContext);
        if (!response.isSuccess()) {
            throw response.getCause();
        }
    }



    /**
     * 从OrderFlowStep构建BaseContext的链ID和链名称
     */
    private BaseContext buildBaseContextChainIdAndChainNameFromStep( OrderFlowStep orderFlowStep,BaseDTO baseDTO) {
        BaseContext baseContext = new BaseContext();
        baseContext.setChainId(orderFlowStep.getChainId());
        baseContext.setChainName(orderFlowStep.getChainName());
        baseContext.setOperatorUserId(baseDTO.getOperatorUserId());
        baseContext.setOperatorUserName(baseDTO.getOperatorUserName());
        return baseContext;
    }

    /**
     * 根据订单类型和事件类型查询流程步骤
     */
    private OrderFlowStep queryFlowStepByOrderTypeAndEventType(String orderType, FlowEventType flowEventType) {
        OrderFlowStep query = new OrderFlowStep();
        query.setOrderType(orderType);
        query.setFlowEventType(flowEventType);
        query.setIsDelete(0); // 确保查询未删除的有效流程
        OrderFlowStep orderFlowStep = orderFlowStepMapper.selectOne(query);
        if (orderFlowStep == null) {
            throw new BusinessException("订单类型[" + orderType + "]下未找到事件类型[" + flowEventType + "]对应的有效流程配置");
        }
        if (orderFlowStep.getChainId() == null || orderFlowStep.getChainId().trim().isEmpty()){
            throw new BusinessException("订单类型[" + orderType + "]下事件类型[" + flowEventType + "]对应的流程配置链ID为空");
        }
        return orderFlowStep;
    }



}
