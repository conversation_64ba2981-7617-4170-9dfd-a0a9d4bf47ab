package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * 表名：order_refund
 * 表注释：订单退款表
*/
@Data
@Table(name = "order_refund")
public class OrderRefund{
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 订单no
     */
    @Column(name = "trade_order_no")
    private String tradeOrderNo;

    /**
     * 订单退款单no
     */
    @Column(name = "order_refund_no")
    private String orderRefundNo;
    
    /**
     * 改价单单号
     */
    @Column(name = "price_change_no")
    private String priceChangeNo;

    /**
     * 退款金额
     */
    @Column(name = "total_amount")
    private Integer totalAmount;

    /**
     * 实退金额
     */
    @Column(name = "received_amount")
    private Integer receivedAmount;

    /**
     * 退款状态
     */
    @Column(name = "refund_status")
    private String refundStatus;

    /**
     * 是否删除 0-否 1-是
     */
    @Column(name = "is_delete")
    private Boolean isDelete;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}