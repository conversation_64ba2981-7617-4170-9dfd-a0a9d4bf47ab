package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * 表名：order_address
 * 表注释：用户地址簿
*/
@Data
public class OrderAddressVO {
    /**
     * 主键ID（自增）
     */
    private Long id;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 关联订单编号
     */
    private String tradeOrderNo;
    /**
     * 收件关联code
     */
    private String receivedAssociationCode;


    /**
     * 收件地址别名
     */
    private String receivedAddressAlias;

    /**
     * 收件人姓名
     */
    private String receivedName;

    /**
     * 收件电话
     */
    private String receivedPhone;

    /**
     * 收件省份/直辖市
     */
    private String receivedProvince;

    /**
     * 收件城市
     */
    private String receivedCity;

    /**
     * 收件区/县
     */
    private String receivedDistrict;

    /**
     * 收件乡/镇
     */
    private String receivedTownship;

    /**
     * 收件详细街道地址
     */
    private String receivedStreet;

    /**
     * 收件省份/直辖市code
     */
    private String receivedProvinceCode;

    /**
     * 收件城市code
     */
    private String receivedCityCode;

    /**
     * 收件区/县code
     */
    private String receivedDistrictCode;

    /**
     * 收件乡/镇code
     */
    private String receivedTownshipCode;

    private Double receivedLongitude;

    private Double receivedLatitude;

    /**
     * 发件人姓名
     */
    private String sendName;
    /**
     * 发件关联code
     */
    private String sendAssociationCode;

    /**
     * 发件地址别名
     */
    private String sendAddressAlias;

    /**
     * 发件电话
     */
    private String sendPhone;

    /**
     * 发件省份/直辖市
     */
    private String sendProvince;

    /**
     * 发件城市
     */
    private String sendCity;

    /**
     * 发件区/县
     */
    private String sendDistrict;

    /**
     * 发件乡/镇
     */
    private String sendTownship;

    /**
     * 发件详细街道地址
     */
    private String sendStreet;

    /**
     * 发件省份/直辖市code
     */
    private String sendProvinceCode;

    /**
     * 发件城市code
     */
    private String sendCityCode;

    /**
     * 发件区/县code
     */
    private String sendDistrictCode;

    /**
     * 发件乡/镇code
     */
    private String sendTownshipCode;

    private Double sendLongitude;

    private Double sendLatitude;


    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}