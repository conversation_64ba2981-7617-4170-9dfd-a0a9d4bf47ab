package com.yuanshuo.platform.order;

import com.yomahub.liteflow.core.FlowExecutor;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.enums.EnumOperatorType;
import com.yuanshuo.platform.order.api.enums.EnumOrderType;
import com.yuanshuo.platform.order.api.enums.EnumSubBizType;
import com.yuanshuo.platform.order.api.model.dto.*;
import com.yuanshuo.platform.order.api.model.vo.OrderRefundVO;
import com.yuanshuo.platform.order.api.model.vo.TradeOrderVO;
import com.yuanshuo.platform.order.mapper.TradeOrderMapper;
import com.yuanshuo.platform.order.rpc.impl.TradeOrderFeignImpl;
import com.yuanshuo.platform.order.server.flow.TradeFlowService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
@SpringBootTest(properties = {
        "spring.profiles.active=local"
})
class MiniSellerTradeFeignTests {
    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    private TradeOrderMapper tradeOrderMapper;
    @Autowired
    private TradeOrderFeignImpl traceOrderFeign;
    @Autowired
    TradeFlowService tradeFlowService;



    /**
     * 创建订单 第一步
     */
    @Test
    public void testInsertOrder() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);
        System.out.println(order);
    }

    public static TradeOrderDTO createTestTradeOrderDTO() {
        // 创建 TradeOrderDTO 对象并设置字段值
        TradeOrderDTO tradeOrderDTO = new TradeOrderDTO();
        tradeOrderDTO.setProcessCode("defaultProcessCode");
        tradeOrderDTO.setOrderType(EnumOrderType.TRADE_LITTLE_SELLER.getCode());
        tradeOrderDTO.setSubBizType(EnumSubBizType.DRIVERLESS_CAR.getCode());
        tradeOrderDTO.setBizType("drone");
        tradeOrderDTO.setChannelType("wechat_mini_program");
        tradeOrderDTO.setUserId(12345L);
        tradeOrderDTO.setNickName("name");
        tradeOrderDTO.setUserPhone("1234567890");
        tradeOrderDTO.setTotalAmount(150);
        tradeOrderDTO.setTotalDiscount(50);
        tradeOrderDTO.setOrderExtData("{\"key\":\"value\"}");

        // 设置商品详情列表
        List<TradeOrderItemDTO> tradeOrderItems = new ArrayList<>();
        tradeOrderItems.add(createTestTradeOrderItemDTO("item001", "service", 50,
                2));
        tradeOrderItems.add(createTestTradeOrderItemDTO("item002", "service",30,
                1));
        tradeOrderDTO.setTradeOrderItems(tradeOrderItems);

        // 设置收货地址
        tradeOrderDTO.setOrderAddress(createTestOrderAddressDTO());
        tradeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        tradeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        return tradeOrderDTO;
    }

    /**
     * 支付订单创建完成回调
     */
    @Test
    public void testCallbackOrderAfterPayCreate() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);
        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);
    }

    /**
     * 支付订单支付成功回调
     */
    @Test
    public void testCallbackOrderAfterPaySuccess() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);
        String tradeOrderNo = order.getData().getTradeOrderNo();
        System.out.println(order);


        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderNo);
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        callbackAfterPaySuccessDTO.setAmount(150);
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);
    }


    /**
     * 支付订单支付失败回调
     */
    @Test
    public void testCallbackOrderAfterPayFail() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);

        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        String tradeOrderNo = order.getData().getTradeOrderNo();
        CallbackOrderAfterPayFailDTO callbackOrderAfterPayFailDTO = new CallbackOrderAfterPayFailDTO();
        callbackOrderAfterPayFailDTO.setTradeOrderNo(tradeOrderNo);
        callbackOrderAfterPayFailDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayFailDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayFail(callbackOrderAfterPayFailDTO);
    }




    /**
     * 用户取消订单
     */
    @Test
    public void testCancelOrder() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);


        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CloseTradeOrderDTO closeOrderDTO = new CloseTradeOrderDTO();
        closeOrderDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        closeOrderDTO.setNeedAudit(true);
        closeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        closeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        traceOrderFeign.cancelOrderByUser(closeOrderDTO);

    }

    /**
     * 支付取消回调
     */
    @Test
    public void testCallbackOrderAfterPayCancel() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);

        CloseTradeOrderDTO closeOrderDTO = new CloseTradeOrderDTO();
        String tradeOrderNo = order.getData().getTradeOrderNo();
        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderNo);
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);


        closeOrderDTO.setTradeOrderNo(tradeOrderNo);
        closeOrderDTO.setNeedAudit(true);
        closeOrderDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        closeOrderDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        traceOrderFeign.cancelOrderByUser(closeOrderDTO);



        CallbackOrderAfterPayCancelDTO callbackOrderAfterPayCancelDTO = new CallbackOrderAfterPayCancelDTO();
        callbackOrderAfterPayCancelDTO.setTradeOrderNo(tradeOrderNo);
        callbackOrderAfterPayCancelDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCancelDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayClose(callbackOrderAfterPayCancelDTO);

    }




    /**
     * 创建退款单
     */
    @Test
    public void testCreateOrderRefund() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);
        String tradeOrderNo = order.getData().getTradeOrderNo();
        System.out.println(order);

        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderNo);
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        callbackAfterPaySuccessDTO.setAmount(150);
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);

        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderNo);
        orderRefundDTO.setRefundAmount(150);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);
        System.out.println(orderRefund);

    }

    /**
     * 支付服务创建退款单回调
     */
    @Test
    public void testCallbackOrderRefundAfterPayCreate() {

        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);
        String tradeOrderNo = order.getData().getTradeOrderNo();
        System.out.println(order);

        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);


        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderNo);
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        callbackAfterPaySuccessDTO.setAmount(150);
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);

        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderNo);
        orderRefundDTO.setRefundAmount(150);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);


        CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO = new CallbackOrderRefundAfterPayCreateDTO();
        callbackOrderRefundAfterPayCreateDTO.setTradeOrderNo(tradeOrderNo);
        callbackOrderRefundAfterPayCreateDTO.setOrderRefundNo(orderRefund.getData().getOrderRefundNo());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderRefundAfterPayCreate(callbackOrderRefundAfterPayCreateDTO);
    }


    /**
     * 支付服务退款成功回调
     */
    @Test
    public void testCallbackOrderRefundAfterPaySuccess() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);
        String tradeOrderNo = order.getData().getTradeOrderNo();
        System.out.println(order);


        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderNo);
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        callbackAfterPaySuccessDTO.setAmount(150);
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);

        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderNo);
        orderRefundDTO.setRefundAmount(150);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);


        CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO = new CallbackOrderRefundAfterPayCreateDTO();
        callbackOrderRefundAfterPayCreateDTO.setTradeOrderNo(tradeOrderNo);
        String orderRefundNo = orderRefund.getData().getOrderRefundNo();
        callbackOrderRefundAfterPayCreateDTO.setOrderRefundNo(orderRefundNo);
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderRefundAfterPayCreate(callbackOrderRefundAfterPayCreateDTO);

        CallbackAfterRefundSuccessDTO callbackAfterRefundSuccessDTO = new CallbackAfterRefundSuccessDTO();
        callbackAfterRefundSuccessDTO.setTradeOrderNo(tradeOrderNo);
        callbackAfterRefundSuccessDTO.setOrderRefundNo(orderRefundNo);
        callbackAfterRefundSuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackAfterRefundSuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderRefundAfterPaySuccess(callbackAfterRefundSuccessDTO);
    }

    /**
     * 支付服务退款失败回调
     */
    @Test
    public void testCallbackOrderRefundAfterPayFail() {
        TradeOrderDTO tradeOrderDTO = createTestTradeOrderDTO();
        R<TradeOrderVO> order = traceOrderFeign.createOrder(tradeOrderDTO);
        String tradeOrderNo = order.getData().getTradeOrderNo();
        System.out.println(order);

        CallbackOrderAfterPayCreateDTO  callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
        callbackOrderAfterPayCreateDTO.setTradeOrderNo(order.getData().getTradeOrderNo());
        callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);

        CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
        callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderNo);
        callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        callbackAfterPaySuccessDTO.setAmount(150);
        tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);

        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setTradeOrderNo(tradeOrderNo);
        orderRefundDTO.setRefundAmount(150);
        orderRefundDTO.setOperatorUserId(EnumOperatorType.PLATFORM_ORDER.getCode());
        orderRefundDTO.setOperatorUserName(EnumOperatorType.PLATFORM_ORDER.getName());
        R<OrderRefundVO> orderRefund = traceOrderFeign.createOrderRefund(orderRefundDTO);


        CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO = new CallbackOrderRefundAfterPayCreateDTO();
        callbackOrderRefundAfterPayCreateDTO.setTradeOrderNo(tradeOrderNo);
        String orderRefundNo = orderRefund.getData().getOrderRefundNo();
        callbackOrderRefundAfterPayCreateDTO.setOrderRefundNo(orderRefundNo);
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderRefundAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderRefundAfterPayCreate(callbackOrderRefundAfterPayCreateDTO);


        CallbackOrderRefundAfterPayFailDTO callbackOrderRefundAfterPayFailDTO = new CallbackOrderRefundAfterPayFailDTO();
        callbackOrderRefundAfterPayFailDTO.setTradeOrderNo(tradeOrderNo);
        callbackOrderRefundAfterPayFailDTO.setOrderRefundNo(orderRefundNo);
        callbackOrderRefundAfterPayFailDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
        callbackOrderRefundAfterPayFailDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
        tradeFlowService.callbackOrderRefundAfterPayFail(callbackOrderRefundAfterPayFailDTO);
    }





//    @Test
//    public void testConfig() {
//
//
//        //创建一个beijing实体类的的测试数据
//        //	TradeOrder record = new TradeOrder();
//        //	tradeOrderMapper.insertSelective(record);
//        TradeOrderQuery tradeOrderQuery = new TradeOrderQuery();
//        tradeOrderQuery.setNickNameLike("123");
//        tradeOrderQuery.setOrderCreateTimeEnd(new Date());
//        List<TradeOrder> tradeOrders = tradeOrderMapper.selectByQuery(tradeOrderQuery);
//        //List<TradeOrder> tradeOrders = tradeOrderMapper.select(new TradeOrder());
//        for (TradeOrder tradeOrder : tradeOrders) {
//            System.out.println(tradeOrder);
//        }
//
//    }

    public static OrderAddressDTO createTestOrderAddressDTO() {
        OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setReceivedAssociationCode("123456");
        addressDTO.setReceivedName("张三");
        addressDTO.setReceivedPhone("1234567890");
        addressDTO.setReceivedProvince("北京市");
        addressDTO.setReceivedCity("北京市");
        addressDTO.setReceivedDistrict("海淀区");
        addressDTO.setReceivedTownship("中关村街道");
        addressDTO.setReceivedStreet("中关村大街1号");
        addressDTO.setReceivedProvinceCode("110000");
        addressDTO.setReceivedCityCode("110100");
        addressDTO.setReceivedDistrictCode("110108");
        addressDTO.setReceivedTownshipCode("110108001");
        addressDTO.setReceivedAddressAlias("hahahah");
        addressDTO.setSendAddressAlias("xixixixix");
        addressDTO.setReceivedLongitude(116.3265);
        addressDTO.setReceivedLatitude(39.9991);

        addressDTO.setSendAssociationCode("654321");
        addressDTO.setSendName("李四");
        addressDTO.setSendPhone("9876543210");
        addressDTO.setSendProvince("上海市");
        addressDTO.setSendCity("上海市");
        addressDTO.setSendDistrict("浦东新区");
        addressDTO.setSendTownship("陆家嘴街道");
        addressDTO.setSendStreet("陆家嘴环路100号");
        addressDTO.setSendProvinceCode("310000");
        addressDTO.setSendCityCode("310100");
        addressDTO.setSendDistrictCode("310115");
        addressDTO.setSendTownshipCode("310115001");
        addressDTO.setSendLongitude(121.5046);
        addressDTO.setSendLatitude(31.2363);
        return addressDTO;
    }

//    @Test
//    public void queryOrderStatistics() {
//        TradeOrderQueryStatisticDTO tradeOrderQueryPageDTO = new TradeOrderQueryStatisticDTO();
//
//        tradeOrderQueryPageDTO.setOrderCreateTimeEnd(new Date());
//        tradeOrderQueryPageDTO.setStatus(EnumTradeOrderStatus.PENDING_PAYMENT.getCode());
//        tradeOrderQueryPageDTO.setNickNameLike("am");
//        tradeOrderQueryPageDTO.setTradeOrderNoLike("1");
//        tradeOrderQueryPageDTO.setReceivedCityCode("110100");
//        tradeOrderQueryPageDTO.setUserPhoneLike("1");
//        R<TradeOrderStatisticsVO> orderStatisticsVOR = traceOrderFeign.queryOrderStatistics(tradeOrderQueryPageDTO);
//        TradeOrderStatisticsVO data = orderStatisticsVOR.getData();
//        System.out.println(data);
//    }
//    @Test
//    public void queryOrderByTradeOrderNo() {
//        TradeOrderQueryOneDTO tradeOrderQueryOneDTO = new TradeOrderQueryOneDTO();
//        tradeOrderQueryOneDTO.setTradeOrderNo("8491106523154801");
//        R<TradeOrderVO> tradeOrderVOR = traceOrderFeign.queryOneOrder(tradeOrderQueryOneDTO);
//        System.out.println(tradeOrderVOR.getData());
//    }


//    @Test
//    public void testQueryOrder() {
//        TradeOrderQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderQueryPageDTO();
//        tradeOrderQueryPageDTO.setPageNo(1);
//        tradeOrderQueryPageDTO.setPageSize(10);
//        tradeOrderQueryPageDTO.setDescs(List.of("id"));
//        tradeOrderQueryPageDTO.setTradeOrderNoLike("4472835923341930");
////        tradeOrderQueryPageDTO.setUserId(12345l);
////        tradeOrderQueryPageDTO.setOrderCreateTimeEnd(DateUtil.endOfMonth(new Date()));
////        tradeOrderQueryPageDTO.setStatus(EnumTradeOrderStatus.PENDING_PAYMENT.getCode());
////        tradeOrderQueryPageDTO.setNickNameLike("am");
////        tradeOrderQueryPageDTO.setTradeOrderNoLike("1");
////        tradeOrderQueryPageDTO.setReceivedCityCode("110100");
////        tradeOrderQueryPageDTO.setUserPhoneLike("1");
//        R<TableDataInfo<TradeOrderVO>> tableDataInfoR = traceOrderFeign.queryPageOrder(tradeOrderQueryPageDTO);
//        TableDataInfo<TradeOrderVO> tableDataInfo = tableDataInfoR.getData();
//        System.out.println(tableDataInfo.getRows().get(0));
//    }
    private static TradeOrderItemDTO createTestTradeOrderItemDTO(String itemCode, String itemType, Integer price,
                                                                 Integer quantity) {
        TradeOrderItemDTO itemDTO = new TradeOrderItemDTO();
        itemDTO.setItemCode(itemCode);
        itemDTO.setItemType(itemType);
        itemDTO.setPrice(price);
        itemDTO.setQuantity(quantity);
        itemDTO.setItemExtData("{\"itemKey\":\"itemValue\"}");
        return itemDTO;
    }

//    @Test
//    public void testQueryListOperatorLog() {
//        TradeOrderLogQueryPageDTO tradeOrderQueryPageDTO = new TradeOrderLogQueryPageDTO();
////        tradeOrderQueryPageDTO.setPageNo(1);
////        tradeOrderQueryPageDTO.setPageSize(10);
////        tradeOrderQueryPageDTO.setDescs(List.of("id"));
//        tradeOrderQueryPageDTO.setTradeOrderNo("2025050916522633010");
//        R<List<OrderOperationLogVO>> listR = traceOrderFeign.queryListOperatorLog(tradeOrderQueryPageDTO);
//
//        System.out.println(JSONUtil.toJsonStr(listR.getData().get(0)));
//    }


}
