<flow>
<!--    &lt;!&ndash; 订单主状态更新 &ndash;&gt;-->
<!--    <chain name="nodeUpdateOrderStatusToPendingPayment">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusTo.bind("status","pending_payment")-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="nodeUpdateOrderStatusToPaid">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusTo.bind("status","paid")-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="nodeUpdateOrderStatusToRefunded">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusTo.bind("status","refunded")-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="nodeUpdateOrderStatusToRefundInitialize">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusTo.bind("status","refund_initialize")-->
<!--        )-->
<!--    </chain>-->

<!--    <chain name="nodeUpdateOrderPaymentStatusToCancelling">-->
<!--        THEN(-->
<!--            nodeUpdatePaymentStatusTo.bind("status","cancelling")-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="nodeUpdateOrderPaymentStatusToCancelled">-->
<!--        THEN(-->
<!--            nodeUpdatePaymentStatusTo.bind("status","cancelled")-->
<!--        )-->
<!--    </chain>-->


<!--    &lt;!&ndash; 同步更新订单状态和订单支付状态 &ndash;&gt;-->
<!--    <chain name="updateOrderStatusAndPaymentStatusToPendingPayment">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusToPendingPayment,-->
<!--            nodeUpdateOrderPaymentStatusToPendingPayment-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="updateOrderStatusAndPaymentStatusToCancelled">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusToCancelled,-->
<!--            nodeUpdateOrderPaymentStatusToCancelled-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="updateOrderStatusAndPaymentStatusToCancelling">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusToCancelling,-->
<!--            nodeUpdateOrderPaymentStatusToCancelling-->
<!--        )-->
<!--    </chain>-->

<!--    &lt;!&ndash; 同步更新订单状态和订单退款状态和退款单退款状态 &ndash;&gt;-->
<!--    <chain name="updateOrderStatusAndRefundStatusAndRefundOrderToRefundInitialize">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusToRefundInitialize,-->
<!--            nodeUpdateOrderRefundStatusToRefundInitialize-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="updateOrderStatusAndRefundStatusAndRefundOrderToRefunding">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusToRefunding,-->
<!--            nodeUpdateOrderRefundStatusToRefunding,-->
<!--            nodeUpdateRefundOrderStatusToRefunding-->
<!--        )-->
<!--    </chain>-->
<!--    <chain name="updateOrderStatusAndRefundStatusAndRefundOrderToRefunded">-->
<!--        THEN(-->
<!--            nodeUpdateOrderStatusToRefunded,-->
<!--            nodeUpdateOrderRefundStatusToRefunded,-->
<!--            nodeUpdateRefundOrderStatusToRefunded-->
<!--        )-->
<!--    </chain>-->

<!--    &lt;!&ndash; 同步更新订单退款状态和退款单退款状态 &ndash;&gt;-->
<!--    <chain name="updateOrderStatusAndRefundStatusAndRefundOrderToRefundFailed">-->
<!--        THEN(-->
<!--            nodeUpdateOrderRefundStatusToRefundFailed,-->
<!--            nodeUpdateRefundOrderStatusToRefundFailed-->
<!--        )-->
<!--    </chain>-->

<!--    &lt;!&ndash; 更新状态为取消中并发送mq &ndash;&gt;-->
<!--    <chain name="cancellingAndSendMq">-->
<!--        THEN(-->
<!--            updateOrderStatusAndPaymentStatusToCancelling,-->
<!--            nodeSendMqForTradeVo-->
<!--        )-->
<!--    </chain>-->












</flow>
