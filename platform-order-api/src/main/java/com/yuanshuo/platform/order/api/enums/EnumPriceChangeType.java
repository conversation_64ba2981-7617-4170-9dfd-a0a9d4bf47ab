package com.yuanshuo.platform.order.api.enums;

/**
 * 订单改价类型枚举
 */
public enum EnumPriceChangeType {
    
    /**
     * 支付前改价
     */
    BEFORE_PAYMENT("before_payment", "支付前改价"),
    
    /**
     * 支付后改价
     */
    AFTER_PAYMENT("after_payment", "支付后改价");
    
    private final String code;
    private final String desc;
    
    EnumPriceChangeType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static EnumPriceChangeType getByCode(String code) {
        for (EnumPriceChangeType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}