package com.yuanshuo.platform.order.api.enums;

public enum EnumChainIdType {

    CREATE_ORDER_FOR_TRADE_LITTLE_SELLER("createOrderForTradeLittleSeller", "销售小工具创建订单"),
    CALLBACK_PAY_CREATE_FOR_TRADE_LITTLE_SELLER("callbackPayCreateForTradeLittleSeller", "支付服务（销售小工具）支付订单创建回调"),
    CALLBACK_PAY_SUCCESS_FOR_TRADE_LITTLE_SELLER("callbackPaySuccessForTradeLittleSeller", "支付服务（销售小工具）支付订单成功回调"),
    CALLBACK_PAY_FAIL_FOR_TRADE_LITTLE_SELLER("callbackPayFailForTradeLittleSeller", "支付服务（销售小工具） 支付订单失败回调"),
    USER_CANCEL_ORDER_FOR_TRADE_LITTLE_SELLER("userCancelOrderForTradeLittleSeller", "用户（销售小工具） 取消订单"),
    OPERATIONS_CANCEL_ORDER_FOR_TRADE_LITTLE_SELLER("operationsCancelOrderForTradeLittleSeller", "运营（销售小工具） 取消订单"),
    TIME_OUT_CANCEL_ORDER_FOR_TRADE_LITTLE_SELLER("timeOutCancelOrderForTradeLittleSeller", "系统超时（销售小工具） 取消订单"),
    CALLBACK_PAY_CLOSE_FOR_TRADE_LITTLE_SELLER("callbackPayCloseForTradeLittleSeller", "支付服务（销售小工具） 支付订单关闭回调-"),
    CREATE_REFUND_ORDER_FOR_TRADE_LITTLE_SELLER("createRefundOrderForTradeLittleSeller", "创建退款单流程"),
    CALLBACK_PAY_REFUND_CREATE_FOR_TRADE_LITTLE_SELLER("callbackPayRefundCreateForTradeLittleSeller", "支付服务退款单创建"),
    CALLBACK_PAY_REFUND_SUCCESS_FOR_TRADE_LITTLE_SELLER("callbackPayRefundSuccessForTradeLittleSeller", "支付服务退款成功"),
    CALLBACK_PAY_REFUND_FAIL_FOR_TRADE_LITTLE_SELLER("callbackPayRefundFailForTradeLittleSeller", "支付服务退款失败"),





    CREATE_ORDER_FOR_APP_PLACE_ORDER("createOrderForAppPlaceOrder", "app创建订单"),
    CALLBACK_PAY_CREATE_FOR_APP_PLACE_ORDER("callbackPayCreateForAppPlaceOrder", "支付服务（app）支付订单创建回调"),
    CALLBACK_PAY_SUCCESS_FOR_APP_PLACE_ORDER("callbackPaySuccessForAppPlaceOrder", "支付服务（app）支付订单成功回调"),
    CALLBACK_PAY_FAIL_FOR_APP_PLACE_ORDER("callbackPayFailForAppPlaceOrder", "支付服务（app） 支付订单失败回调"),
    USER_CANCEL_ORDER_FOR_APP_PLACE_ORDER("userCancelOrderForAppPlaceOrder", "用户（app） 取消订单"),
    OPERATIONS_CANCEL_ORDER_FOR_APP_PLACE_ORDER("operationsCancelOrderForAppPlaceOrder", "运营（app） 取消订单"),
    TIME_OUT_CANCEL_ORDER_FOR_APP_PLACE_ORDER("timeOutCancelOrderForAppPlaceOrder", "系统超时（app） 取消订单"),
    CALLBACK_PAY_CLOSE_FOR_APP_PLACE_ORDER("callbackPayCloseForAppPlaceOrder", "支付服务（app） 支付订单关闭回调-"),
    CREATE_REFUND_ORDER_FOR_APP_PLACE_ORDER("createRefundOrderForAppPlaceOrder", "创建退款单流程"),
    CALLBACK_PAY_REFUND_CREATE_FOR_APP_PLACE_ORDER("callbackPayRefundCreateForAppPlaceOrder", "支付服务退款单创建"),
    CALLBACK_PAY_REFUND_SUCCESS_FOR_APP_PLACE_ORDER("callbackPayRefundSuccessForAppPlaceOrder", "支付服务退款成功"),
    CALLBACK_PAY_REFUND_FAIL_FOR_APP_PLACE_ORDER("callbackPayRefundFailForAppPlaceOrder", "支付服务退款失败"),

    ;

    private String chainId;
    private String name;

    EnumChainIdType(String chainId, String name) {
        this.chainId = chainId;
        this.name = name;
    }

    public String getChainId() {
        return chainId;
    }
    public String getName() {
        return name;
    }

}
