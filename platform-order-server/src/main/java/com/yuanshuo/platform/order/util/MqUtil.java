package com.yuanshuo.platform.order.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MqUtil {
    @Autowired
    RocketMQTemplate rocketMQTemplate;
    @Value("${spring.profiles.active}")
    String active;
    public void sendPlatformOrderMq(String tag, Object param,String key) {
        try {
            log.info("sendPlatformOrderMq tag:{} param:{}",tag, param);
//            rocketMQTemplate.convertAndSend("platform-pay-" + active + ":" + tag
//                    , param);
            rocketMQTemplate.syncSendOrderly("platform-order-" + active + ":" + tag
                    , param,key);
        } catch (Exception e) {
           log.error("sendPlatformOrderMq error", e);
        }
    }
}
