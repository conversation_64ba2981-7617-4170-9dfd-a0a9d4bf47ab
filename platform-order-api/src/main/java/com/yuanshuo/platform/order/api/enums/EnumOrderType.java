package com.yuanshuo.platform.order.api.enums;

public enum EnumOrderType {

    TRADE_LITTLE_SELLER("trade_little_seller", "销售小工具"),
    APP_PLACE_ORDER("app_place_order", "app下单");


    private String code;

    private String msg;

    EnumOrderType(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
