<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderFlowMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderFlow">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_order" jdbcType="VARCHAR" property="createOrder" />
    <result column="callback_pay_create" jdbcType="VARCHAR" property="callbackPayCreate" />
    <result column="callback_pay_success" jdbcType="VARCHAR" property="callbackPaySuccess" />
    <result column="callback_pay_fail" jdbcType="VARCHAR" property="callbackPayFail" />
    <result column="user_cancel_order" jdbcType="VARCHAR" property="userCancelOrder" />
    <result column="operations_cancel_order" jdbcType="VARCHAR" property="operationsCancelOrder" />
    <result column="time_out_cancel_order" jdbcType="VARCHAR" property="timeOutCancelOrder" />
    <result column="callback_pay_close" jdbcType="VARCHAR" property="callbackPayClose" />
    <result column="create_refund_order" jdbcType="VARCHAR" property="createRefundOrder" />
    <result column="callback_pay_refund_create" jdbcType="VARCHAR" property="callbackPayRefundCreate" />
    <result column="callback_pay_refund_success" jdbcType="VARCHAR" property="callbackPayRefundSuccess" />
    <result column="callback_pay_refund_fail" jdbcType="VARCHAR" property="callbackPayRefundFail" />
    <result column="callback_fulfillment_create" jdbcType="VARCHAR" property="callbackFulfillmentCreate" />
    <result column="callback_fulfillment_transport" jdbcType="VARCHAR" property="callbackFulfillmentTransport" />
    <result column="callback_fulfillment_complete" jdbcType="VARCHAR" property="callbackFulfillmentComplete" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>