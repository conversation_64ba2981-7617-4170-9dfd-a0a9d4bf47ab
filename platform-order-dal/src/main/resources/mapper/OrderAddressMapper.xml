<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderAddressMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderAddress">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="received_association_code" jdbcType="VARCHAR" property="receivedAssociationCode" />
    <result column="received_address_alias" jdbcType="VARCHAR" property="receivedAddressAlias" />
    <result column="received_name" jdbcType="VARCHAR" property="receivedName" />
    <result column="received_phone" jdbcType="VARCHAR" property="receivedPhone" />
    <result column="received_province" jdbcType="VARCHAR" property="receivedProvince" />
    <result column="received_city" jdbcType="VARCHAR" property="receivedCity" />
    <result column="received_district" jdbcType="VARCHAR" property="receivedDistrict" />
    <result column="received_township" jdbcType="VARCHAR" property="receivedTownship" />
    <result column="received_street" jdbcType="VARCHAR" property="receivedStreet" />
    <result column="received_province_code" jdbcType="VARCHAR" property="receivedProvinceCode" />
    <result column="received_city_code" jdbcType="VARCHAR" property="receivedCityCode" />
    <result column="received_district_code" jdbcType="VARCHAR" property="receivedDistrictCode" />
    <result column="received_township_code" jdbcType="VARCHAR" property="receivedTownshipCode" />
    <result column="received_longitude" jdbcType="DOUBLE" property="receivedLongitude" />
    <result column="received_latitude" jdbcType="DOUBLE" property="receivedLatitude" />
    <result column="send_association_code" jdbcType="VARCHAR" property="sendAssociationCode" />
    <result column="send_address_alias" jdbcType="VARCHAR" property="sendAddressAlias" />
    <result column="send_name" jdbcType="VARCHAR" property="sendName" />
    <result column="send_phone" jdbcType="VARCHAR" property="sendPhone" />
    <result column="send_province" jdbcType="VARCHAR" property="sendProvince" />
    <result column="send_city" jdbcType="VARCHAR" property="sendCity" />
    <result column="send_district" jdbcType="VARCHAR" property="sendDistrict" />
    <result column="send_township" jdbcType="VARCHAR" property="sendTownship" />
    <result column="send_street" jdbcType="VARCHAR" property="sendStreet" />
    <result column="send_province_code" jdbcType="VARCHAR" property="sendProvinceCode" />
    <result column="send_city_code" jdbcType="VARCHAR" property="sendCityCode" />
    <result column="send_district_code" jdbcType="VARCHAR" property="sendDistrictCode" />
    <result column="send_township_code" jdbcType="VARCHAR" property="sendTownshipCode" />
    <result column="send_longitude" jdbcType="DOUBLE" property="sendLongitude" />
    <result column="send_latitude" jdbcType="DOUBLE" property="sendLatitude" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>