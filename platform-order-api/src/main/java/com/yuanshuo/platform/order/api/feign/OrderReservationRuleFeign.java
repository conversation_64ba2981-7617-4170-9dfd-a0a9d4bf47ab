package com.yuanshuo.platform.order.api.feign;

import com.yuanshuo.common.entity.support.TableDataInfo;
import com.yuanshuo.common.entity.web.R;
import com.yuanshuo.platform.order.api.model.dto.query.OrderReservationRuleQueryDTO;
import com.yuanshuo.platform.order.dto.OrderReservationRuleDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderReservationRuleVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 预约下单规则配置Feign接口
 */
@FeignClient(value = "platform-order")
public interface OrderReservationRuleFeign {
    
    /**
     * 创建预约下单规则
     * @param ruleDTO 规则信息
     * @return 操作结果
     */
    @RequestMapping(value = "/reservation-rule/create", method = RequestMethod.POST)
    R<Void> createRule(@RequestBody OrderReservationRuleDTO ruleDTO);
    
    /**
     * 更新预约下单规则
     * @param ruleDTO 规则信息
     * @return 操作结果
     */
    @RequestMapping(value = "/reservation-rule/update", method = RequestMethod.POST)
    R<Void> updateRule(@RequestBody OrderReservationRuleDTO ruleDTO);
    
    /**
     * 删除预约下单规则
     * @param ruleId 规则ID
     * @param operatorUserId 操作人ID
     * @param operatorUserName 操作人姓名
     * @return 操作结果
     */
    @RequestMapping(value = "/reservation-rule/delete", method = RequestMethod.POST)
    R<Void> deleteRule(@RequestParam("ruleId") String ruleId,
                       @RequestParam("operatorUserId") String operatorUserId,
                       @RequestParam("operatorUserName") String operatorUserName);
    
    /**
     * 查询预约下单规则列表
     * @param queryDTO 查询条件
     * @return 规则列表
     */
    @RequestMapping(value = "/reservation-rule/list", method = RequestMethod.POST)
    R<TableDataInfo<OrderReservationRuleVO>> queryRuleList(@RequestBody OrderReservationRuleQueryDTO queryDTO);
    
    /**
     * 根据规则ID查询规则详情
     * @param ruleId 规则ID
     * @return 规则详情
     */
    @RequestMapping(value = "/reservation-rule/detail", method = RequestMethod.GET)
    R<OrderReservationRuleVO> getRuleDetail(@RequestParam("ruleId") String ruleId);
    
    /**
     * 根据城市编码查询预约规则
     * @param cityCode 城市编码
     * @return 规则信息
     */
    @RequestMapping(value = "/reservation-rule/getByCityCode", method = RequestMethod.GET)
    R<OrderReservationRuleVO> getRuleByCityCode(@RequestParam("cityCode") String cityCode);
}