package com.yuanshuo.platform.order.mq;

import cn.hutool.json.JSONUtil;
import com.yuanshuo.platform.order.api.enums.EnumFulfillmentStatus;
import com.yuanshuo.platform.order.api.enums.EnumOperatorType;
import com.yuanshuo.platform.order.api.model.dto.FulfillmentCallbackDTO;
import com.yuanshuo.platform.order.server.flow.TradeFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MqCallBackFulfillment {

    @Value("${spring.profiles.active}")
    String active;
    @Value("${rocketmq.name-server}")
    String nameSrvAddr;
    @Value("${platform-order.lock.wait-time:5}")
    private long waitTime;

    @Value("${platform-order.lock.lease-time:30}")
    private long leaseTime;
    @Autowired
    private TradeFlowService tradeFlowService;

    @Bean
    public void createFulfillmentConsumer() throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();
        consumer.setNamesrvAddr(nameSrvAddr);

        consumer.subscribe("transport-" + active, "*");
        consumer.setConsumerGroup("transport_to_platform-order-"+ active);
        consumer.registerMessageListener((MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    String topic = msg.getTopic();
                    String tags = msg.getTags();
                    String body = new String(msg.getBody(), "UTF-8");
                    log.info("收到消息 -> topic: {}, tags: {}, body: {}", topic, tags, body);
                    FulfillmentCallbackDTO fulfillmentCallbackDTO = JSONUtil.toBean(body, FulfillmentCallbackDTO.class);
                    fulfillmentCallbackDTO.setOperatorUserId(EnumOperatorType.FULFILLMENT.getCode());
                    fulfillmentCallbackDTO.setOperatorUserName(EnumOperatorType.FULFILLMENT.getName());

                    if(fulfillmentCallbackDTO.getStatus().equals(EnumFulfillmentStatus.DISPATCH_PENDING.getCode())){
                        tradeFlowService.callbackOrderAfterFulfillmentCreate(fulfillmentCallbackDTO);
                    } else if (fulfillmentCallbackDTO.getStatus().equals(EnumFulfillmentStatus.DISPATCHED.getCode())) {
                        tradeFlowService.callbackOrderAfterFulfillmentTransport(fulfillmentCallbackDTO);
                    } else if (fulfillmentCallbackDTO.getStatus().equals(EnumFulfillmentStatus.SUCCESS.getCode())) {
                        tradeFlowService.callbackOrderAfterFulfillmentComplete(fulfillmentCallbackDTO);
                    }
                } catch (Exception e) {
                    log.error("消息处理失败，稍后重试", e);
                    // 重试
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
        consumer.start();
        System.out.println("Consumer started for topic: " + "transport-" + active);
    }
}
