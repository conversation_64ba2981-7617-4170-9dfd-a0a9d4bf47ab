package com.yuanshuo.platform.order.mapper.po;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

/**
 * 表名：order_item
 * 表注释：订单明细表
*/
@Data
@Table(name = "order_item")
public class OrderItem {
    /**
     * 主键ID（自增）
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 订单明细表no
     */
    @Column(name = "order_item_no")
    private String orderItemNo;

    /**
     * 关联订单表id
     */
    @Column(name = "trade_order_no")
    private String tradeOrderNo;

    /**
     * 商品名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 商品code
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 商品类型（1实物商品 2服务 3权益）
     */
    @Column(name = "item_type")
    private String itemType;

    /**
     * 单价
     */
    private Integer price;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 总价
     */
    private Integer subtotal;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}