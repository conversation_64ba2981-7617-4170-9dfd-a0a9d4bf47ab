package com.yuanshuo.platform.order.mq;

import cn.hutool.json.JSONUtil;
import com.yuanshuo.platform.order.api.enums.EnumOperatorType;
import com.yuanshuo.platform.order.api.model.dto.*;
import com.yuanshuo.platform.order.mapper.po.OrderRefund;
import com.yuanshuo.platform.order.server.OrderRefundService;
import com.yuanshuo.platform.order.server.flow.PriceChangeFlowService;
import com.yuanshuo.platform.order.server.flow.TradeFlowService;
import com.yuanshuo.platform.order.util.LockExecutorUtil;
import com.yuanshuo.platform.pay.api.enums.EnumBusinessType;
import com.yuanshuo.platform.pay.api.enums.EnumPayMqSendTag;
import com.yuanshuo.platform.pay.api.model.vo.PaymentReceiptRefundVO;
import com.yuanshuo.platform.pay.api.model.vo.PaymentReceiptVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class MqCallBackPay {
    @Value("${spring.profiles.active}")
    String active;
    @Value("${rocketmq.name-server}")
    String nameSrvAddr;
    @Value("${platform-order.lock.wait-time:5}")
    private long waitTime;

    @Value("${platform-order.lock.lease-time:30}")
    private long leaseTime;
    @Autowired
    private TradeFlowService tradeFlowService;
    @Autowired
    private PriceChangeFlowService priceChangeFlowService;
    @Autowired
    private OrderRefundService orderRefundService;

    @Autowired
    private LockExecutorUtil lockExecutorUtil;

    @Bean
    public void createPayConsumer() throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();
        consumer.setNamesrvAddr(nameSrvAddr);
        consumer.subscribe("platform-pay-" + active, "*");
        consumer.setConsumerGroup("platform-pay_to_platform-order-"+ active);
        consumer.registerMessageListener((MessageListenerConcurrently) (msgs, context) -> {
            for (MessageExt msg : msgs) {
                try {
                    String topic = msg.getTopic();
                    String tags = msg.getTags();
                    String body = new String(msg.getBody(), "UTF-8");
                    log.info("收到消息 -> topic: {}, tags: {}, body: {}", topic, tags, body);

                    if(EnumPayMqSendTag.PAYMENT_RECEIPT_CLOSE.getCode().equals(tags)){
                        PaymentReceiptVO paymentReceiptVO = JSONUtil.toBean(body, PaymentReceiptVO.class);

                        if (paymentReceiptVO.getBusinessType().equals(EnumBusinessType.SUPPLEMENT_PAY)) {
                            //补款单关闭不做任何操作
                            continue;
                        }
                        String tradeOrderNo = paymentReceiptVO.getBusinessNo();
                        lockExecutorUtil.executeWithLock(tradeOrderNo, waitTime, leaseTime, () -> {
                            CallbackOrderAfterPayCancelDTO callbackOrderAfterPayCancelDTO = new CallbackOrderAfterPayCancelDTO();
                            callbackOrderAfterPayCancelDTO.setTradeOrderNo(tradeOrderNo);
                            callbackOrderAfterPayCancelDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                            callbackOrderAfterPayCancelDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                            tradeFlowService.callbackOrderAfterPayClose(callbackOrderAfterPayCancelDTO);
                            return callbackOrderAfterPayCancelDTO;
                        });

                    } else if (EnumPayMqSendTag.PAYMENT_RECEIPT_CREATE.getCode().equals(tags)) {
                        // 收到支付单创建回调
                        PaymentReceiptVO paymentReceiptVO = JSONUtil.toBean(body, PaymentReceiptVO.class);
                        String tradeOrderNo = paymentReceiptVO.getBusinessNo();
                        lockExecutorUtil.executeWithLock(tradeOrderNo, waitTime, leaseTime, () -> {
                            CallbackOrderAfterPayCreateDTO callbackOrderAfterPayCreateDTO = new CallbackOrderAfterPayCreateDTO();
                            callbackOrderAfterPayCreateDTO.setTradeOrderNo(tradeOrderNo);
                            callbackOrderAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                            callbackOrderAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                            tradeFlowService.callbackOrderAfterPayCreate(callbackOrderAfterPayCreateDTO);
                            return callbackOrderAfterPayCreateDTO;
                        });
                    } else if (EnumPayMqSendTag.PAYMENT_RECEIPT_SUCCESS.getCode().equals(tags)) {
                        // 收到支付成功回调
                        PaymentReceiptVO paymentReceiptVO = JSONUtil.toBean(body, PaymentReceiptVO.class);
                        String tradeOrderNo = paymentReceiptVO.getBusinessNo();
                        if (paymentReceiptVO.getBusinessType().equals(EnumBusinessType.ORDER_PAY.getCode())) {
                            lockExecutorUtil.executeWithLock(tradeOrderNo, waitTime, leaseTime, () -> {
                                CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
                                callbackAfterPaySuccessDTO.setBusinessNo(tradeOrderNo);
                                callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                                callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                                callbackAfterPaySuccessDTO.setAmount(paymentReceiptVO.getTotalAmount());
                                callbackAfterPaySuccessDTO.setPayChannel(paymentReceiptVO.getPayChannel());
                                tradeFlowService.callbackOrderAfterPaySuccess(callbackAfterPaySuccessDTO);
                                return callbackAfterPaySuccessDTO;
                            });
                        } else if (paymentReceiptVO.getBusinessType().equals(EnumBusinessType.SUPPLEMENT_PAY.getCode())) {
                            //改价单支付成功
                            CallbackAfterPaySuccessDTO callbackAfterPaySuccessDTO = new CallbackAfterPaySuccessDTO();
                            callbackAfterPaySuccessDTO.setBusinessNo(paymentReceiptVO.getBusinessNo());
                            callbackAfterPaySuccessDTO.setAmount(paymentReceiptVO.getTotalAmount());
                            callbackAfterPaySuccessDTO.setPayChannel( paymentReceiptVO.getPayChannel());
                            callbackAfterPaySuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                            callbackAfterPaySuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                            callbackAfterPaySuccessDTO.setPaymentReceiptNo(paymentReceiptVO.getPaymentReceiptNo());
                            priceChangeFlowService.callbackPriceChangeAfterPaySuccess(callbackAfterPaySuccessDTO);
                        }
                    } else if (EnumPayMqSendTag.PAYMENT_RECEIPT_FAIL.getCode().equals(tags)) {
                        // 收到支付失败回调
                        PaymentReceiptVO paymentReceiptVO = JSONUtil.toBean(body, PaymentReceiptVO.class);
                        String tradeOrderNo = paymentReceiptVO.getBusinessNo();
                        lockExecutorUtil.executeWithLock(tradeOrderNo, waitTime, leaseTime, () -> {
                            CallbackOrderAfterPayFailDTO callbackOrderAfterPayFailDTO = new CallbackOrderAfterPayFailDTO();
                            callbackOrderAfterPayFailDTO.setTradeOrderNo(tradeOrderNo);
                            callbackOrderAfterPayFailDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                            callbackOrderAfterPayFailDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                            tradeFlowService.callbackOrderAfterPayFail(callbackOrderAfterPayFailDTO);
                            return callbackOrderAfterPayFailDTO;
                        });
                    } else if (EnumPayMqSendTag.PAYMENT_RECEIPT_REFUND_CREATE.getCode().equals(tags)) {
                        //收到退款创建回调
                        PaymentReceiptRefundVO paymentReceiptRefundVO = JSONUtil.toBean(body, PaymentReceiptRefundVO.class);
                        String tradeOrderNo = paymentReceiptRefundVO.getBusinessNo();
                        lockExecutorUtil.executeWithLock(tradeOrderNo, waitTime, leaseTime, () -> {
                            CallbackOrderRefundAfterPayCreateDTO callbackOrderRefundAfterPayCreateDTO = new CallbackOrderRefundAfterPayCreateDTO();
                            callbackOrderRefundAfterPayCreateDTO.setTradeOrderNo(tradeOrderNo);
                            callbackOrderRefundAfterPayCreateDTO.setOrderRefundNo(paymentReceiptRefundVO.getTradeOrderRefundNo());
                            callbackOrderRefundAfterPayCreateDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                            callbackOrderRefundAfterPayCreateDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                            tradeFlowService.callbackOrderRefundAfterPayCreate(callbackOrderRefundAfterPayCreateDTO);
                            return callbackOrderRefundAfterPayCreateDTO;
                        });
                    } else if (EnumPayMqSendTag.PAYMENT_RECEIPT_REFUND_SUCCESS.getCode().equals(tags)) {
                        //收到退款成功回调
                        PaymentReceiptRefundVO paymentReceiptRefundVO = JSONUtil.toBean(body, PaymentReceiptRefundVO.class);
                        //根据paymentReceiptRefundVO.getTradeOrderRefundNo()查询退款单号
                        OrderRefund orderRefund = orderRefundService.queryByOrderRefundNoMust(paymentReceiptRefundVO.getTradeOrderRefundNo());
                        if (orderRefund.getPriceChangeNo() == null) {
                            String tradeOrderNo = orderRefund.getTradeOrderNo();
                            lockExecutorUtil.executeWithLock(tradeOrderNo, waitTime, leaseTime, () -> {
                                CallbackAfterRefundSuccessDTO callbackAfterRefundSuccessDTO = new CallbackAfterRefundSuccessDTO();
                                callbackAfterRefundSuccessDTO.setTradeOrderNo(tradeOrderNo);
                                callbackAfterRefundSuccessDTO.setOrderRefundNo(paymentReceiptRefundVO.getTradeOrderRefundNo());
                                callbackAfterRefundSuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                                callbackAfterRefundSuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                                tradeFlowService.callbackOrderRefundAfterPaySuccess(callbackAfterRefundSuccessDTO);
                                return callbackAfterRefundSuccessDTO;
                            });
                        } else {
                            CallbackAfterRefundSuccessDTO callbackAfterRefundSuccessDTO = new CallbackAfterRefundSuccessDTO();
                            callbackAfterRefundSuccessDTO.setAmount(paymentReceiptRefundVO.getTotalAmount());
                            callbackAfterRefundSuccessDTO.setPayChannel(paymentReceiptRefundVO.getPayChannel());
                            callbackAfterRefundSuccessDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                            callbackAfterRefundSuccessDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                            callbackAfterRefundSuccessDTO.setPaymentReceiptRefundNo(paymentReceiptRefundVO.getPaymentReceiptRefundNo());
                            callbackAfterRefundSuccessDTO.setOrderRefundNo(orderRefund.getOrderRefundNo());
                            priceChangeFlowService.callbackPriceChangeAfterRefundSuccess(callbackAfterRefundSuccessDTO);
                        }

                    } else if (EnumPayMqSendTag.PAYMENT_RECEIPT_REFUND_FAIL.getCode().equals(tags)) {
                        //收到退款失败回调
                        PaymentReceiptRefundVO paymentReceiptRefundVO = JSONUtil.toBean(body, PaymentReceiptRefundVO.class);
                        String tradeOrderNo = paymentReceiptRefundVO.getBusinessNo();
                        lockExecutorUtil.executeWithLock(tradeOrderNo, waitTime, leaseTime, () -> {
                            CallbackOrderRefundAfterPayFailDTO callbackOrderRefundAfterPayFailDTO = new CallbackOrderRefundAfterPayFailDTO();
                            callbackOrderRefundAfterPayFailDTO.setTradeOrderNo(tradeOrderNo);
                            callbackOrderRefundAfterPayFailDTO.setOrderRefundNo(paymentReceiptRefundVO.getTradeOrderRefundNo());
                            callbackOrderRefundAfterPayFailDTO.setOperatorUserId(EnumOperatorType.PLATFORM_PAY.getCode());
                            callbackOrderRefundAfterPayFailDTO.setOperatorUserName(EnumOperatorType.PLATFORM_PAY.getName());
                            tradeFlowService.callbackOrderRefundAfterPayFail(callbackOrderRefundAfterPayFailDTO);
                            return callbackOrderRefundAfterPayFailDTO;
                        });
                    }

                } catch (Exception e) {
                    log.error("消息处理失败，稍后重试", e);
                    // 重试
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
        consumer.start();
        System.out.println("Consumer started for topic: " + "platform-pay-" + active);
    }
}

