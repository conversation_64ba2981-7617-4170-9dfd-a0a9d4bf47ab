package com.yuanshuo.platform.order.api.enums;

public enum EnumTradeOrderStatus {

    INITIALIZE("initialize","订单初始化"),
    PAYING("paying", "支付中"),
    FULFILLING("fulfilling", "履约中"),
    COMPLETED("completed", "已完成"),
    CANCELLING("cancelling", "取消中"),
    CANCELLED("cancelled", "已取消"),
    REFUNDING("refunding","退款中"),
    CLOSED("closed", "已关闭"),

    ;

    private String code;
    private String msg;

    EnumTradeOrderStatus(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
    //判断入参是否为该枚举的值
    public static boolean isValid(String code) {
        for (EnumTradeOrderStatus value : EnumTradeOrderStatus.values()) {
            if (value.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
    //根据code获取枚举值
    public static EnumTradeOrderStatus getByCode(String code) {
        for (EnumTradeOrderStatus value : EnumTradeOrderStatus.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
