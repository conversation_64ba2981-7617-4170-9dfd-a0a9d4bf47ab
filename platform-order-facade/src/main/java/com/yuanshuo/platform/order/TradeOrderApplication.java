package com.yuanshuo.platform.order;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import tk.mybatis.spring.annotation.MapperScan;

@SpringBootApplication
@ComponentScan({"com.yuanshuo.platform"})
@MapperScan(basePackages = "com.yuanshuo.platform.order.mapper")
@EnableDiscoveryClient
@EnableFeignClients
public class TradeOrderApplication {

	public static void main(String[] args) {
		SpringApplication.run(TradeOrderApplication.class, args);
	}

}
