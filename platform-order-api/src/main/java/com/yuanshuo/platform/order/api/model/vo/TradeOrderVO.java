package com.yuanshuo.platform.order.api.model.vo;

import com.yuanshuo.platform.order.api.enums.EnumTradeOrderStatus;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 表名：trade_order
 * 表注释：订单主表
*/
@Data
public class TradeOrderVO {
    /**
     * 主键ID（自增）
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 父订单id（指向本表订单编号）
     */
    private String parentNo;

    /**
     * 订单编号
     */
    private String tradeOrderNo;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 渠道类型,支付宝小程序/微信小程序等等
     */
    private String channelType;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 联系电话
     */
    private String userPhone;

    /**
     * 总金额（主订单有效）
     */
    private Integer totalAmount;

    /**
     * 总优惠金额
     */
    private Integer totalDiscount;

    /**
     * 总实付金额
     */
    private Integer totalPaid;
    
    /**
     * 总应付金额
     */
    private Integer totalPayable;

    /**
     * 总退款金额
     */
    private Integer totalRefund;

    /**
     * 订单总状态
     *  @see EnumTradeOrderStatus#getCode()
     */
    private String status;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 履约状态
     */
    private String fulfillmentStatus;

    /**
     * 地址编号
     */
    private String orderAddressId;

    /**
     * 收件省份/直辖市code
     */
    private String receivedProvinceCode;

    /**
     * 收件城市code
     */
    private String receivedCityCode;

    /**
     * 收件区/县code
     */
    private String receivedDistrictCode;

    /**
     * 发件省份/直辖市code
     */
    private String sendProvinceCode;

    /**
     * 发件城市code
     */
    private String sendCityCode;

    /**
     * 发件区/县code
     */
    private String sendDistrictCode;
    /**
     * 订单超时时间
     */
    private Date orderExpireTime;
    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    private String createUser;

    private Date createTime;

    private Date updateTime;

    List<OrderItemVO> orderItems;

    OrderAddressVO orderAddress;

    /**
     * 订单扩展数据,json格式,不会做特殊处理
     */
    private String orderExtData;

    /**
     * 预定履约时间
     */
    private Date reserveTime;

    /**
     * 是否实时单 1-是 0-否
     */
    private Boolean actualOrderType;

    /**
     * 支付渠道
     */
    private String payChannel;

    List<OrderRefundVO> orderRefundVOList;

    private List<TradeOrderVO> childOrders;

    private List<OrderPriceChangeVO> orderPriceChangeVOS;
}