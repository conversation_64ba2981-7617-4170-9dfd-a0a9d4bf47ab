<flow>
    <!--只包括跟支付有关的编排文件-->
    <!-- 支付服务（app）支付订单创建回调 -->
    <chain name="callbackPayCreateForAppPlaceOrder">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToPaying"},{"status":"paid","process":"nodeWarnProcess"}]';
        cmpDataPay = '[{"paymentStatus":null,"process":"nodeUpdateOrderPaymentStatusToPendingPayment"}]';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            ) .to(
                nodeUpdateOrderStatusToPaying,
                nodeWarnProcess.bind("msg","支付订单创建回调告警")
            ),
            SWITCH(
                switchPaymentStatusToProcess.bind("switchPaymentStatus",cmpDataPay)
            ) .to(
                nodeUpdateOrderPaymentStatusToPendingPayment
            ),
            syncStatus
        );
    </chain>

    <!-- 支付服务（app）支付订单成功回调 -->
    <chain name="callbackPaySuccessForAppPlaceOrder">
        cmpData = '[{"paymentStatus":"pending_payment","process":"nodeUpdateOrderPaymentStatusToPaid"}]';
        mqDataSeller = '{"tag":"order_paid"}';
        THEN(
            SWITCH(
                switchPaymentStatusToProcess.bind("switchPaymentStatus",cmpData)
            ).to(
                nodeUpdateOrderPaymentStatusToPaid
            ),
            syncStatus,
            nodeSendMqForTradeVo.bind("mqData",mqDataSeller)
        );
    </chain>


    <!-- 支付服务（app）改价订单成功回调 -->
    <chain name="callbackPriceChangeOrderPaidSuccess">
        THEN(
            nodeUpdatePriceChangeToPaid
        );
    </chain>



    <!-- 支付服务（app） 支付订单失败回调 -->
    <chain name="callbackPayFailForAppPlaceOrder">
        cmpData = '[{"paymentStatus":"pending_payment","process":"nodeUpdateOrderPaymentStatusToFailed"}]';
        THEN(
            SWITCH(
                switchPaymentStatusToProcess.bind("switchPaymentStatus",cmpData)
            ).to(
                nodeUpdateOrderPaymentStatusToFailed
            ),
            syncStatus
        );
    </chain>

    <!-- 支付服务（app）支付订单关闭回调 -->
    <chain name="callbackPayCloseForAppPlaceOrder">
        cmpData = '[{"status":"cancelling","process":"nodeUpdateOrderStatusToCancelled"},{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"}]';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            )
            .to(
                nodeUpdateOrderStatusToCancelled
            ),
            nodeUpdateOrderPaymentStatusToCancelled,
            syncStatus
        );
    </chain>

    <!-- 支付服务（销售小工具）支付订单创建回调 -->
    <chain name="callbackPayCreateForTradeLittleSeller">
        cmpData = '[{"status":"initialize","process":"nodeUpdateOrderStatusToPaying"},{"status":"paid","process":"nodeWarnProcess"}]';
        cmpDataPay = '[{"paymentStatus":null,"process":"nodeUpdateOrderPaymentStatusToPendingPayment"}]';
        THEN(
            SWITCH(
                switchStatusToProcess.bind("switchStatus",cmpData)
            ) .to(
                nodeUpdateOrderStatusToPaying,
                nodeWarnProcess.bind("msg","支付订单创建回调告警")
            ),
            SWITCH(
                switchPaymentStatusToProcess.bind("switchPaymentStatus",cmpDataPay)
            ) .to(
                nodeUpdateOrderPaymentStatusToPendingPayment
            )
        );
    </chain>

    <!-- 支付服务（销售小工具）支付订单成功回调 -->
    <chain name="callbackPaySuccessForTradeLittleSeller">
        cmpData = '[{"paymentStatus":"pending_payment","process":"nodeEmptyProcess"}]';
        mqDataSeller = '{"tag":"order_paid"}';
        THEN(
            SWITCH(
                switchPaymentStatusToProcess.bind("switchPaymentStatus",cmpData)
            ).to(
                nodeWarnProcess,
                nodeEmptyProcess
            ),
            nodeUpdateOrderPaymentStatusToPaid,
            nodeUpdateOrderStatusToCompleted,
            nodeSendMqForTradeVo.bind("mqData",mqDataSeller)
        );
    </chain>

    <!-- 支付服务（销售小工具） 支付订单失败回调 -->
    <chain name="callbackPayFailForTradeLittleSeller">
        cmpData = '[{"paymentStatus":"pending_payment","process":"nodeEmptyProcess"}]';
        THEN(
            SWITCH(
                switchPaymentStatusToProcess.bind("switchPaymentStatus",cmpData)
            ).to(
                nodeWarnProcess,
                nodeEmptyProcess
            ),
            nodeUpdateOrderPaymentStatusToFailed
        );
    </chain>

    <!-- 支付服务（销售小工具）支付订单关闭回调 -->
    <chain name="callbackPayCloseForTradeLittleSeller">
        cmpData = '[{"status":"cancelling","process":"nodeUpdateOrderStatusToCancelled"},{"status":"initialize","process":"nodeUpdateOrderStatusToCancelled"}]';

        SWITCH(
            switchStatusToProcess.bind("switchStatus",cmpData)
        )
        .to(
            nodeUpdateOrderStatusToCancelled
        );
    </chain>

</flow>
