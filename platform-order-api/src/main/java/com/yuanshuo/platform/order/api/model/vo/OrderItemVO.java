package com.yuanshuo.platform.order.api.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * 表名：order_item
 * 表注释：订单明细表
*/
@Data
public class OrderItemVO {
    /**
     * 主键ID（自增）
     */
    private Long id;

    /**
     * 订单明细表no
     */
    private String orderItemNo;

    /**
     * 关联订单表id
     */
    private String tradeOrderNo;

    /**
     * 商品code
     */
    private String itemCode;

    /**
     * 商品类型（1实物商品 2服务 3权益）
     */
    private String itemType;

    /**
     * 单价
     */
    private Integer price;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 总价
     */
    private Integer subtotal;

    /**
     * 创建记录的用户id,若没有则为当前服务名
     */
    private String createUser;

    private Date createTime;

    private Date updateTime;


    /**
     * 商品扩展信息
     */
    private String itemExtData;
}