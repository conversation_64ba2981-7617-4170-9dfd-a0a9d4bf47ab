package com.yuanshuo.platform.order.api.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 预约下单规则配置DTO
 */
@Data
public class OrderReservationRuleDTO extends BaseDTO{
    
    /**
     * 规则ID（编辑时必填）
     */
    private String ruleId;
    
    /**
     * 城市编码列表
     */
    private List<String> cityCodes;
    
    /**
     * 可以预约未来几天
     */
    private Integer futureDays;
    
    /**
     * 时间间隔（分钟）
     */
    private Integer timeInterval;
    
    /**
     * 规则状态（0: 禁用, 1: 启用）
     */
    private Integer status;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 是否允许城市转移（当城市存在规则配置冲突时，是否将老规则中的城市删除并添加到新规则中）
     * true: 允许转移，false: 不允许转移（默认）
     */
    private Boolean allowCityTransfer = false;
    

}