package com.yuanshuo.platform.order.controller;

import com.github.pagehelper.PageInfo;
import com.yuanshuo.platform.order.api.model.dto.query.AdministrativeDivisionQueryDTO;
import com.yuanshuo.platform.order.api.model.vo.AdministrativeDivisionVO;
import com.yuanshuo.platform.order.server.AdministrativeDivisionService;
import com.yuanshuo.common.entity.web.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 行政区划控制器
 */
@Slf4j
@RestController
@RequestMapping("/administrative-division")
public class AdministrativeDivisionController {

    @Autowired
    private AdministrativeDivisionService administrativeDivisionService;

    /**
     * 模糊查询城市
     * @param queryDTO 查询条件
     * @return 分页城市列表
     */
    @PostMapping("/search")
    public R<PageInfo<AdministrativeDivisionVO>> searchCities(@RequestBody AdministrativeDivisionQueryDTO queryDTO) {
        try {
            PageInfo<AdministrativeDivisionVO> result = administrativeDivisionService.searchCities(queryDTO);
            return R.success(result);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 编码转中文名
     * @param queryDTO 查询条件（包含编码列表）
     * @return 编码与中文名的映射关系
     */
    @PostMapping("/code-to-name")
    public R<Map<String, String>> convertCodeToName(@RequestBody AdministrativeDivisionQueryDTO queryDTO) {
        try {
            Map<String, String> result = administrativeDivisionService.convertCodeToName(queryDTO);
            return R.success(result);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据单个编码获取中文名
     * @param code 行政区编码
     * @return 中文名称
     */
    @GetMapping("/name/{code}")
    public R<String> getNameByCode(@PathVariable String code) {
        try {
            String result = administrativeDivisionService.getNameByCode(code);
            return R.success(result);
        } catch (Exception e) {
            return R.fail("查询失败: " + e.getMessage());
        }
    }
}