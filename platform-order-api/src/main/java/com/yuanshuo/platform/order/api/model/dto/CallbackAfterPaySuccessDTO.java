package com.yuanshuo.platform.order.api.model.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 订单入口入参
 */
@Data
public class CallbackAfterPaySuccessDTO extends BaseDTO{

    @NotEmpty
    private String businessNo;
    /**
     * 金额
     */
    @NotNull
    private Integer amount;

    /**
     * 渠道类型
     */

    private String payChannel;

    private String paymentReceiptNo;

}
