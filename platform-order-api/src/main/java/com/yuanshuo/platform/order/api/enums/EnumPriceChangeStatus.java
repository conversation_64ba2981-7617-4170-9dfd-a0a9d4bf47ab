package com.yuanshuo.platform.order.api.enums;

/**
 * 订单改价状态枚举
 */
public enum EnumPriceChangeStatus {
    
    /**
     * 未改价
     */
    UNCHANGED("unchanged", "未改价"),
    
    /**
     * 改价中
     */
    CHANGING("changing", "改价中"),
    
    /**
     * 已改价
     */
    CHANGED("changed", "已改价");
    
    private final String code;
    private final String desc;
    
    EnumPriceChangeStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据code获取枚举
     */
    public static EnumPriceChangeStatus getByCode(String code) {
        for (EnumPriceChangeStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}