package com.yuanshuo.platform.order.api.enums;

public enum EnumPayCallbackType {

    PAY("pay", "支付"),
    REFUNDED("refunded", "退款");
    private String code;
    private String msg;
    EnumPayCallbackType(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
