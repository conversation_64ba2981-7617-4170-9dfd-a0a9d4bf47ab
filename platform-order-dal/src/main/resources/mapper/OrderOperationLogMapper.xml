<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanshuo.platform.order.mapper.OrderOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.yuanshuo.platform.order.mapper.po.OrderOperationLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="operator_user_id" jdbcType="VARCHAR" property="operatorUserId" />
    <result column="operator_user_name" jdbcType="VARCHAR" property="operatorUserName" />
    <result column="operator_time" jdbcType="TIMESTAMP" property="operatorTime" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="chain_id" jdbcType="VARCHAR" property="chainId" />
    <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="before_data" jdbcType="LONGVARCHAR" property="beforeData" />
    <result column="after_data" jdbcType="LONGVARCHAR" property="afterData" />
  </resultMap>
    <select id="selectByQuery" resultMap="BaseResultMap">
      select * from order_operation_log
      <where>
        <if test="tradeOrderNo != null and tradeOrderNo != ''">
          and trade_order_no = #{tradeOrderNo}
        </if>
        <if test="nodeIdList !=null and nodeIdList.size()>0">
          AND node_id IN
          <foreach collection="nodeIdList" item="id" open="(" separator="," close=")">
            #{id}
          </foreach>
        </if>
        <if test="chainIdList !=null and chainIdList.size()>0">
          AND chain_id IN
          <foreach collection="chainIdList" item="id" open="(" separator="," close=")">
            #{id}
          </foreach>
        </if>
      </where>
    </select>
</mapper>