package com.yuanshuo.platform.order.mapper.enums;

/**
 * 流程事件类型枚举
 * 对应原order_flow表中的各个流程字段
 */
public enum FlowEventType {
    CREATE_ORDER("create_order", "创建订单"),
    CALLBACK_PAY_CREATE("callback_pay_create", "支付创建回调"),
    CALLBACK_PAY_SUCCESS("callback_pay_success", "支付成功回调"),
    CALLBACK_PAY_FAIL("callback_pay_fail", "支付失败回调"),
    USER_CANCEL_ORDER("user_cancel_order", "用户取消订单"),
    OPERATIONS_CANCEL_ORDER("operations_cancel_order", "运营取消订单"),
    TIME_OUT_CANCEL_ORDER("time_out_cancel_order", "超时取消订单"),
    CALLBACK_PAY_CLOSE("callback_pay_close", "支付关闭回调"),
    CREATE_REFUND_ORDER("create_refund_order", "创建退款订单"),
    CALLBACK_PAY_REFUND_CREATE("callback_pay_refund_create", "退款创建回调"),
    CALLBACK_PAY_REFUND_SUCCESS("callback_pay_refund_success", "退款成功回调"),
    CALLBACK_PAY_REFUND_FAIL("callback_pay_refund_fail", "退款失败回调"),
    CALLBACK_FULFILLMENT_CREATE("callback_fulfillment_create", "履约创建回调"),
    CALLBACK_FULFILLMENT_TRANSPORT("callback_fulfillment_transport", "履约运输回调"),
    CALLBACK_FULFILLMENT_COMPLETE("callback_fulfillment_complete", "履约完成回调"),

    //改价单
    //创建改价单
    CREATE_PRICE_CHANGE_ORDER("create_price_change_order", "创建改价单"),
    //改价单支付成功
    CALLBACK_PRICE_CHANGE_ORDER_PAID_SUCCESS("callback_price_change_order_paid_success", "改价单支付成功回调"),
    //改价单退款成功
    CALLBACK_PRICE_CHANGE_ORDER_REFUND_SUCCESS("callback_price_change_order_refund_success", "改价单退款成功回调"),
    ;
    private final String code;
    private final String description;

    FlowEventType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     */
    public static FlowEventType fromCode(String code) {
        for (FlowEventType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown FlowEventType code: " + code);
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}