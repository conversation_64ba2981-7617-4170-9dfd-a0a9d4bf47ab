package com.yuanshuo.platform.order.api.model.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
@Data
public class OrderRefundDTO extends BaseDTO{

    /**
     * 订单no
     */
    @NotEmpty
    private String tradeOrderNo;
    
    /**
     * 改价单单号
     */
    private String priceChangeNo;

    /**
     * 退款金额
     */
    @NotNull
    private Integer refundAmount;



}
