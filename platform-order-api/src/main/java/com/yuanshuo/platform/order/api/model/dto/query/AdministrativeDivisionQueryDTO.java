package com.yuanshuo.platform.order.api.model.dto.query;

import com.yuanshuo.common.entity.support.PageQuery;
import lombok.Data;

import java.util.List;

/**
 * 行政区划查询DTO
 */
@Data
public class AdministrativeDivisionQueryDTO extends PageQuery {
    
    /**
     * 行政区名称关键字（用于模糊查询）
     */
    private String name;

    /**
     * 级别：province=省 city=市 district=区/县 street=镇
     */
    private String level;

    /**
     * 行政区编码（用于精确查询）
     */
    private String code;

    /**
     * 行政区编码列表（用于批量查询）
     */
    private List<String> codes;
}