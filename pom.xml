<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yuanshuo</groupId>
		<artifactId>yuanshuo-starter-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>

	<groupId>com.yuanshuo</groupId>
	<artifactId>platform-order</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>platform-order</name>
	<description>platform-order project for Spring Boot</description>


	<modules>
		<module>platform-order-api</module>
		<module>platform-order-server</module>
		<module>platform-order-dal</module>
		<module>platform-order-facade</module>
	</modules>
	<properties>
		<java.version>17</java.version>
	</properties>

	<dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>com.yuanshuo</groupId>
			<artifactId>platform-pay-api</artifactId>
			<version>1.0.3-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-spring-boot-starter</artifactId>
			<version>2.3.3</version>
		</dependency>
		<!-- 添加 Redisson 依赖 -->
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson-spring-boot-starter</artifactId>
			<version>3.27.1</version>
		</dependency>
		<dependency>
			<groupId>com.yuanshuo</groupId>
			<artifactId>platform-order-server</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>2.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.yuanshuo</groupId>
			<artifactId>platform-order-api</artifactId>
			<version>1.0.6-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.yuanshuo</groupId>
			<artifactId>platform-order-dal</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.yuanshuo</groupId>
			<artifactId>platform-order-facade</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.37</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-alibaba-dependencies</artifactId>
			<version>2023.0.3.2</version>
			<type>pom</type>
			<scope>import</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-dependencies</artifactId>
			<version>2023.0.1</version>
			<type>pom</type>
			<scope>import</scope>
		</dependency>
		<dependency>
			<groupId>tk.mybatis</groupId>
			<artifactId>mapper-spring-boot-starter</artifactId>
			<version>5.0.1</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.25</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<version>4.2.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.skywalking</groupId>
			<artifactId>apm-toolkit-logback-1.x</artifactId>
			<version>9.4.0</version>
		</dependency>
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.skywalking</groupId>
			<artifactId>apm-toolkit-trace</artifactId>
			<version>9.4.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>3.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-spring-boot-starter</artifactId>
			<version>2.13.2</version>
		</dependency>
	</dependencies>
	</dependencyManagement>

</project>
