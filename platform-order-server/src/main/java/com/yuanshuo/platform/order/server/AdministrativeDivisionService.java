package com.yuanshuo.platform.order.server;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yuanshuo.platform.order.api.model.dto.query.AdministrativeDivisionQueryDTO;
import com.yuanshuo.platform.order.api.model.vo.AdministrativeDivisionVO;
import com.yuanshuo.platform.order.mapper.AdministrativeDivisionMapper;
import com.yuanshuo.platform.order.mapper.po.AdministrativeDivision;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 行政区划服务
 */
@Slf4j
@Service
public class AdministrativeDivisionService {

    @Autowired
    private AdministrativeDivisionMapper administrativeDivisionMapper;

    /**
     * 模糊查询城市
     * @param queryDTO 查询条件
     * @return 分页城市列表
     */
    public PageInfo<AdministrativeDivisionVO> searchCities(AdministrativeDivisionQueryDTO queryDTO) {
        log.info("查询城市，查询条件：{}", queryDTO);
        
        // 设置分页参数，默认第1页，每页20条
        int pageNo = queryDTO.getPageNo() != null ? queryDTO.getPageNo() : 1;
        int pageSize = queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 20;
        PageHelper.startPage(pageNo, pageSize);
        
        List<AdministrativeDivision> divisions;
        
        // 如果没有传入名称条件，则查询所有数据（分页）
        if (!StringUtils.hasText(queryDTO.getName())) {
            log.info("未传入查询条件，返回所有行政区划数据（分页）");
            divisions = administrativeDivisionMapper.selectAll(queryDTO.getLevel());
        } else {
            // 有名称条件时进行模糊查询
            divisions = administrativeDivisionMapper.selectByNameLike(
                    queryDTO.getName(), queryDTO.getLevel());
        }
        
        // 转换为VO
        List<AdministrativeDivisionVO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(divisions)) {
            for (AdministrativeDivision division : divisions) {
                AdministrativeDivisionVO vo = convertToVO(division);
                result.add(vo);
            }
        }
        
        PageInfo<AdministrativeDivisionVO> pageInfo = new PageInfo<>(result);
        log.info("查询城市完成，返回第{}页，共{}条记录", pageInfo.getPageNum(), pageInfo.getTotal());
        return pageInfo;
    }

    /**
     * 根据编码转换为中文名
     * @param queryDTO 查询条件（包含编码列表）
     * @return 编码与中文名的映射关系
     */
    public Map<String, String> convertCodeToName(AdministrativeDivisionQueryDTO queryDTO) {
        log.info("根据编码转换为中文名，查询条件：{}", queryDTO);
        
        Map<String, String> result = new HashMap<>();
        
        // 单个编码查询
        if (StringUtils.hasText(queryDTO.getCode())) {
            AdministrativeDivision division = administrativeDivisionMapper.selectByCode(queryDTO.getCode());
            if (division != null) {
                result.put(division.getCode(), division.getName());
            }
        }
        
        // 批量编码查询
        if (!CollectionUtils.isEmpty(queryDTO.getCodes())) {
            List<AdministrativeDivision> divisions = administrativeDivisionMapper.selectByCodes(queryDTO.getCodes());
            if (!CollectionUtils.isEmpty(divisions)) {
                for (AdministrativeDivision division : divisions) {
                    result.put(division.getCode(), division.getName());
                }
            }
        }
        
        log.info("编码转换中文名完成，返回{}条记录", result.size());
        return result;
    }

    /**
     * 根据单个编码获取中文名
     * @param code 行政区编码
     * @return 中文名称
     */
    public String getNameByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return null;
        }
        
        AdministrativeDivision division = administrativeDivisionMapper.selectByCode(code);
        return division != null ? division.getName() : null;
    }

    /**
     * 转换为VO对象
     * @param division 实体对象
     * @return VO对象
     */
    private AdministrativeDivisionVO convertToVO(AdministrativeDivision division) {
        AdministrativeDivisionVO vo = new AdministrativeDivisionVO();
        BeanUtils.copyProperties(division, vo);
        return vo;
    }
}