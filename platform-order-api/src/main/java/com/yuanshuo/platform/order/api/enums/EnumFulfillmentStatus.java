package com.yuanshuo.platform.order.api.enums;

public enum EnumFulfillmentStatus {

    DISPATCH_PENDING("dispatch_pending","待派车"),
    DISPATCHED("dispatched", "已派车"),
    DISPATCH_FAIL("dispatch_fail", "派车失败"),
    PICKUP_PENDING("pickup_pending", "待取货"),
    PICKUP_PROCESSING("pickup_processing", "取货中"),
    TRANSITING("transiting", "运输中"),
    UNLOADING("unloading", "卸货中"),
    SUCCESS("success", "运输完成"),
    FAIL("fail", "运输失败");

    private String code;
    private String msg;

    EnumFulfillmentStatus(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static boolean isValid(String code) {
        for (EnumFulfillmentStatus value : EnumFulfillmentStatus.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
    public static EnumFulfillmentStatus getByCode(String code) {
        for (EnumFulfillmentStatus value : EnumFulfillmentStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
