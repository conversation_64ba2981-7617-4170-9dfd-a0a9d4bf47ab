package com.yuanshuo.platform.order.api.model.dto.query;

import lombok.Data;

import java.util.Date;

/**
 * 订单入口入参
 */
@Data
public class TradeOrderQueryStatisticDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 城市code, 精确匹配
     */
    private String receivedCityCode;

    /**
     * 开始时间
     */
    private Date orderCreateTimeStart;

    /**
     * 结束时间
     */
    private Date orderCreateTimeEnd;

    /**
     * 订单编号, 模糊查询
     */
    private String tradeOrderNoLike;

    /**
     * 商户名称, 模糊查询
     */
    private String nickNameLike;

    /**
     * 收件人手机号, 模糊查询
     */
    private String userPhoneLike;

    /**
     * 订单状态(单选)
     */
    private String status;

    //订单类型 如销售小工具
    private String orderType;
}