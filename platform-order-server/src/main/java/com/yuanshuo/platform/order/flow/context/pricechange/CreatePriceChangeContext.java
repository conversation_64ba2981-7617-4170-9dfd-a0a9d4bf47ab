package com.yuanshuo.platform.order.flow.context.pricechange;

import cn.hutool.core.bean.BeanUtil;
import com.yuanshuo.platform.order.api.model.dto.OrderPriceChangeDTO;
import com.yuanshuo.platform.order.api.model.vo.OrderPriceChangeVO;
import com.yuanshuo.platform.order.flow.context.BaseContext;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CreatePriceChangeContext extends BaseContext {
    /**
     * 创建订单的入参DTO
     */
    private OrderPriceChangeDTO orderPriceChangeDTO;

    private OrderPriceChangeVO orderPriceChangeVO;

    public CreatePriceChangeContext(BaseContext baseContext) {
        BeanUtil.copyProperties(baseContext, this);
    }

}
